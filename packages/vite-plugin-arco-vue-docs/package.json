{"name": "@arco-design/vite-plugin-arco-vue-docs", "version": "0.1.0", "description": "Provides Markdown to Vue Component in Arco Vue Docs", "author": "wangchen <<EMAIL>>", "license": "MIT", "main": "dist/index.js", "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/arco-design/arco-design-vue.git"}, "scripts": {"dev": "tsc --watch", "build": "tsc", "lint-staged": "npx lint-staged"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --fix", "prettier --write"]}, "peerDependencies": {"@vitejs/plugin-vue": "^1.2.3", "vite": "^2.4.0"}, "devDependencies": {"@types/js-yaml": "^4.0.9", "@types/marked": "^2.0.5", "@types/prismjs": "^1.26.5", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "eslint": "^7.32.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-config-prettier": "^8.10.0", "eslint-import-resolver-typescript": "^2.7.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^3.4.1", "prettier": "^2.8.8"}, "dependencies": {"@vitejs/plugin-vue": "^1.2.3", "@vue/compiler-sfc": "^3.5.13", "js-yaml": "^4.1.0", "marked": "^2.1.3", "prismjs": "^1.29.0", "vite": "^2.4.0"}}