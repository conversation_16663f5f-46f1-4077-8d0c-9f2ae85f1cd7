{"name": "@arco-design/arco-vue-docs-navbar", "private": true, "version": "0.2.0", "description": "Arco Vue 2.0 Docs NavBar", "author": "ArcoDesign Team", "license": "MIT", "main": "dist/index.js", "files": ["dist"], "scripts": {"build": "vite build -c vite.config.ts && npm run dtsgen", "dtsgen": "tsc", "less": "lessc src/navbar.less src/navbar.css"}, "devDependencies": {"@arco-design/web-react": "^2.65.0", "@arco-materials/site-navbar-new": "^1.2.13", "@svgr/core": "^5.5.0", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "esbuild": "^0.12.29", "eslint": "^7.32.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-config-prettier": "^8.10.0", "eslint-import-resolver-typescript": "^2.7.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^3.4.1", "less": "^4.2.2", "prettier": "^2.8.8", "react": "^17.0.2", "react-dom": "^17.0.2", "react-transition-group": "^4.4.5", "vite": "^2.9.18"}, "installConfig": {"hoistingLimits": "workspaces"}}