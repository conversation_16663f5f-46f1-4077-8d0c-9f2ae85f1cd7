{
  "compilerOptions": {
    "outDir": "es",
    "target": "ES2015",
    "module": "ES2020",
    "moduleResolution": "node",
    "removeComments": true,
    "strict": true,
    "allowJs": true,
    "isolatedModules": true,
    "esModuleInterop": true,
    "jsx": "preserve",
    "jsxImportSource": "vue",
    "lib": ["ESNext", "DOM", "DOM.Iterable"],
    "skipLibCheck": true,
    "resolveJsonModule": true,
  },
  "include": [
    "components/**/*",
    "icon/**/*",
    "*.d.ts"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "lib",
    "es",
    "json",
    "**/__tests__/**/*"
  ]
}
