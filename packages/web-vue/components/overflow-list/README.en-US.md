```yaml
meta:
  type: Component
  category: Data Display
title: List
description:
```

*Auto translate by google.*

@import ./__demo__/basic.md

@import ./__demo__/from.md

## API


### `<overflow-list>` Props

|Attribute|Description|Type|Default|
|---|---|---|:---:|
|min|Minimum number of elements to display|`number`|`0`|
|margin|Item Margin|`number`|`8`|
|from|Overflow From|`'start' \| 'end'`|`'end'`|
### `<overflow-list>` Events

|Event Name|Description|Parameters|
|---|---|---|
|change|Triggered when the overflow quantity changes|value: `number`|
### `<overflow-list>` Slots

|Slot Name|Description|Parameters|
|---|---|---|
|overflow|Overflow|number: `number`|



