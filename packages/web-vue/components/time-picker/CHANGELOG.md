```yaml
changelog: true
```

## 2.56.0

`2024-07-26`

### 🐛 BugFix

- TriggerProps property not correctly passed through ([#3178](https://github.com/arco-design/arco-design-vue/pull/3178))


## 2.55.3

`2024-06-07`

### 🐛 BugFix

- Fix issue with read-only mode still being editable ([#3173](https://github.com/arco-design/arco-design-vue/pull/3173))
- Added `placeholder` type definition ([#3173](https://github.com/arco-design/arco-design-vue/pull/3173))


## 2.44.6

`2023-03-31`

### 🐛 BugFix

- Fix the bubbling behavior of the clear event. ([#2271](https://github.com/arco-design/arco-design-vue/pull/2271))


## 2.41.0

`2022-12-30`

### 🆕 Feature

- `time-picker` supports prefix slots ([#1997](https://github.com/arco-design/arco-design-vue/pull/1997))


## 2.35.1

`2022-08-19`

### 🐛 BugFix

- Fix default value of size property in time-picker ([#1513](https://github.com/arco-design/arco-design-vue/pull/1513))

