// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<time-picker> demo: render [basic] correctly 1`] = `
"<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 194px;\\" visible=\\"false\\" editable=\\"true\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择时间\\" class=\\"arco-picker-start-time\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-clock-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M24 14v10h9.5m8.5 0c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>"
`;

exports[`<time-picker> demo: render [control] correctly 1`] = `
"<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 194px;\\" visible=\\"false\\" editable=\\"true\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择时间\\" class=\\"arco-picker-start-time\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-clock-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M24 14v10h9.5m8.5 0c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>"
`;

exports[`<time-picker> demo: render [default-value] correctly 1`] = `
"<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 194px; margin-right: 24px; margin-bottom: 24px;\\" visible=\\"false\\" editable=\\"true\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择时间\\" class=\\"arco-picker-start-time\\" value=\\"18:24:23\\"></div>
  <div class=\\"arco-picker-suffix\\"><span class=\\"arco-icon-hover arco-picker-icon-hover arco-picker-clear-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-clock-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M24 14v10h9.5m8.5 0c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<div class=\\"arco-picker arco-picker-range arco-picker-size-medium\\" style=\\"width: 252px; margin-bottom: 24px;\\" visible=\\"false\\" editable=\\"true\\">
  <!--v-if-->
  <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"开始时间\\" value=\\"09:24:53\\"></div><span class=\\"arco-picker-separator\\"> - </span>
  <div class=\\"arco-picker-input\\"><input placeholder=\\"结束时间\\" value=\\"18:44:33\\"></div>
  <div class=\\"arco-picker-suffix\\"><span class=\\"arco-icon-hover arco-picker-icon-hover arco-picker-clear-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-clock-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M24 14v10h9.5m8.5 0c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>"
`;

exports[`<time-picker> demo: render [disable-confirm] correctly 1`] = `
"<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 194px; margin: 0px 24px 24px 0px;\\" visible=\\"false\\" editable=\\"true\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择时间\\" class=\\"arco-picker-start-time\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-clock-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M24 14v10h9.5m8.5 0c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<div class=\\"arco-picker arco-picker-range arco-picker-size-medium\\" style=\\"width: 252px; margin: 0px 24px 24px 0px;\\" visible=\\"false\\" editable=\\"true\\">
  <!--v-if-->
  <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"开始时间\\"></div><span class=\\"arco-picker-separator\\"> - </span>
  <div class=\\"arco-picker-input\\"><input placeholder=\\"结束时间\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-clock-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M24 14v10h9.5m8.5 0c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>"
`;

exports[`<time-picker> demo: render [disabled] correctly 1`] = `
"<div class=\\"arco-picker arco-picker-size-medium arco-picker-disabled\\" style=\\"margin: 0px 24px 24px 0px;\\" visible=\\"false\\" editable=\\"true\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input disabled=\\"\\" placeholder=\\"请选择时间\\" class=\\"arco-picker-start-time\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-clock-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M24 14v10h9.5m8.5 0c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<div class=\\"arco-picker arco-picker-range arco-picker-size-medium arco-picker-disabled\\" style=\\"width: 252px; margin: 0px 24px 24px 0px;\\" visible=\\"false\\" editable=\\"true\\">
  <!--v-if-->
  <div class=\\"arco-picker-input arco-picker-input-active\\"><input disabled=\\"\\" placeholder=\\"开始时间\\"></div><span class=\\"arco-picker-separator\\"> - </span>
  <div class=\\"arco-picker-input\\"><input disabled=\\"\\" placeholder=\\"结束时间\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-clock-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M24 14v10h9.5m8.5 0c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>"
`;

exports[`<time-picker> demo: render [disabled-time] correctly 1`] = `
"<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 194px; margin: 0px 24px 24px 0px;\\" visible=\\"false\\" editable=\\"true\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择时间\\" class=\\"arco-picker-start-time\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-clock-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M24 14v10h9.5m8.5 0c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<div class=\\"arco-picker arco-picker-range arco-picker-size-medium\\" style=\\"width: 252px; margin: 0px 24px 24px 0px;\\" visible=\\"false\\" editable=\\"true\\">
  <!--v-if-->
  <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"开始时间\\"></div><span class=\\"arco-picker-separator\\"> - </span>
  <div class=\\"arco-picker-input\\"><input placeholder=\\"结束时间\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-clock-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M24 14v10h9.5m8.5 0c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>"
`;

exports[`<time-picker> demo: render [extra] correctly 1`] = `
"<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 194px;\\" visible=\\"false\\" editable=\\"true\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择时间\\" class=\\"arco-picker-start-time\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-clock-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M24 14v10h9.5m8.5 0c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>"
`;

exports[`<time-picker> demo: render [format] correctly 1`] = `
"<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 130px;\\" visible=\\"false\\" editable=\\"true\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择时间\\" class=\\"arco-picker-start-time\\" value=\\"09:24\\"></div>
  <div class=\\"arco-picker-suffix\\"><span class=\\"arco-icon-hover arco-picker-icon-hover arco-picker-clear-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-clock-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M24 14v10h9.5m8.5 0c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>"
`;

exports[`<time-picker> demo: render [prefix] correctly 1`] = `
"<div>
  <div>
    <div class=\\"arco-picker arco-picker-size-medium arco-picker-has-prefix\\" style=\\"width: 194px;\\" visible=\\"false\\" editable=\\"true\\">
      <div class=\\"arco-picker-prefix\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-info-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
          <path d=\\"M24 20v14m0-16v-4m18 10c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path>
        </svg></div>
      <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择时间\\" class=\\"arco-picker-start-time\\"></div>
      <div class=\\"arco-picker-suffix\\">
        <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-clock-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M24 14v10h9.5m8.5 0c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path></svg></span>
        <!--v-if-->
      </div>
    </div>
    <!---->
  </div>
  <div>
    <div class=\\"arco-picker arco-picker-range arco-picker-size-medium arco-picker-has-prefix\\" style=\\"width: 252px; margin-top: 20px;\\" visible=\\"false\\" editable=\\"true\\">
      <div class=\\"arco-picker-prefix\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-info-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
          <path d=\\"M24 20v14m0-16v-4m18 10c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path>
        </svg></div>
      <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"开始时间\\"></div><span class=\\"arco-picker-separator\\"> - </span>
      <div class=\\"arco-picker-input\\"><input placeholder=\\"结束时间\\"></div>
      <div class=\\"arco-picker-suffix\\">
        <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-clock-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M24 14v10h9.5m8.5 0c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path></svg></span>
        <!--v-if-->
      </div>
    </div>
    <!---->
  </div>
</div>"
`;

exports[`<time-picker> demo: render [rangepicker] correctly 1`] = `
"<div class=\\"arco-picker arco-picker-range arco-picker-size-medium\\" style=\\"width: 252px;\\" visible=\\"false\\" editable=\\"true\\">
  <!--v-if-->
  <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"开始时间\\"></div><span class=\\"arco-picker-separator\\"> - </span>
  <div class=\\"arco-picker-input\\"><input placeholder=\\"结束时间\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-clock-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M24 14v10h9.5m8.5 0c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>"
`;

exports[`<time-picker> demo: render [size] correctly 1`] = `
"<div style=\\"margin-bottom: 20px;\\"><span class=\\"arco-radio-group-button arco-radio-group-size-medium arco-radio-group-direction-horizontal\\"><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"mini\\"><span class=\\"arco-radio-button-content\\">mini</span></label><label class=\\"arco-radio-button arco-radio-checked\\"><input type=\\"radio\\" checked=\\"\\" class=\\"arco-radio-target\\" value=\\"small\\"><span class=\\"arco-radio-button-content\\">small</span></label><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"medium\\"><span class=\\"arco-radio-button-content\\">medium</span></label><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"large\\"><span class=\\"arco-radio-button-content\\">large</span></label></span></div>
<div class=\\"arco-picker arco-picker-size-small\\" style=\\"width: 194px;\\" visible=\\"false\\" editable=\\"true\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择时间\\" class=\\"arco-picker-start-time\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-clock-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M24 14v10h9.5m8.5 0c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>"
`;

exports[`<time-picker> demo: render [step] correctly 1`] = `
"<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 194px;\\" visible=\\"false\\" editable=\\"true\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择时间\\" class=\\"arco-picker-start-time\\" value=\\"10:25:30\\"></div>
  <div class=\\"arco-picker-suffix\\"><span class=\\"arco-icon-hover arco-picker-icon-hover arco-picker-clear-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-clock-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M24 14v10h9.5m8.5 0c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>"
`;

exports[`<time-picker> demo: render [use-12-hours] correctly 1`] = `
"<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 194px; margin: 0px 24px 24px 0px;\\" visible=\\"false\\" editable=\\"true\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择时间\\" class=\\"arco-picker-start-time\\" value=\\"12:20:20 AM\\"></div>
  <div class=\\"arco-picker-suffix\\"><span class=\\"arco-icon-hover arco-picker-icon-hover arco-picker-clear-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-clock-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M24 14v10h9.5m8.5 0c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 194px; margin: 0px 24px 24px 0px;\\" visible=\\"false\\" editable=\\"true\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择时间\\" class=\\"arco-picker-start-time\\" value=\\"09:20:20 pm\\"></div>
  <div class=\\"arco-picker-suffix\\"><span class=\\"arco-icon-hover arco-picker-icon-hover arco-picker-clear-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-clock-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M24 14v10h9.5m8.5 0c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 194px; margin: 0px 24px 24px 0px;\\" visible=\\"false\\" editable=\\"true\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择时间\\" class=\\"arco-picker-start-time\\" value=\\"2:20 AM\\"></div>
  <div class=\\"arco-picker-suffix\\"><span class=\\"arco-icon-hover arco-picker-icon-hover arco-picker-clear-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-clock-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M24 14v10h9.5m8.5 0c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<div class=\\"arco-picker arco-picker-range arco-picker-size-medium\\" style=\\"width: 300px; margin: 0px 24px 24px 0px;\\" visible=\\"false\\" editable=\\"true\\">
  <!--v-if-->
  <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"开始时间\\" value=\\"12:20:20 AM\\"></div><span class=\\"arco-picker-separator\\"> - </span>
  <div class=\\"arco-picker-input\\"><input placeholder=\\"结束时间\\" value=\\"08:30:30 PM\\"></div>
  <div class=\\"arco-picker-suffix\\"><span class=\\"arco-icon-hover arco-picker-icon-hover arco-picker-clear-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-clock-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M24 14v10h9.5m8.5 0c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>"
`;
