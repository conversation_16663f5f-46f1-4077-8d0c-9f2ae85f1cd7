```yaml
changelog: true
```

## 2.55.0

`2024-03-15`

### 🆕 新增功能

- 新增 `input-value` 相关属性 ([#3024](https://github.com/arco-design/arco-design-vue/pull/3024))


## 2.49.1

`2023-07-24`

### 🐛 问题修复

- 修复 `border` 属性默认值错误的问题 ([#2580](https://github.com/arco-design/arco-design-vue/pull/2580))


## 2.49.0

`2023-07-21`

### 🆕 新增功能

- 增加空状态下 header 和 footer 的显示状态 ([#2573](https://github.com/arco-design/arco-design-vue/pull/2573))

### 🐛 问题修复

- 修复 border 属性失效的问题 ([#2568](https://github.com/arco-design/arco-design-vue/pull/2568))


## 2.47.1

`2023-06-09`

### 🐛 问题修复

- 修复在虚拟列表下搜索内容不可选择的问题 ([#2488](https://github.com/arco-design/arco-design-vue/pull/2488))


## 2.47.0

`2023-06-02`

### 🆕 新增功能

- 增加树选择器页头和页脚插槽 ([#2417](https://github.com/arco-design/arco-design-vue/pull/2417))


## 2.39.1

`2022-11-25`

### 🆎 类型修正

- 更新trigger-props的类型 ([#1885](https://github.com/arco-design/arco-design-vue/pull/1885))


## 2.39.0

`2022-11-18`

### 🆕 新增功能

- 替换虚拟滚动条组件，增加 scrollbar 属性 ([#1872](https://github.com/arco-design/arco-design-vue/pull/1872))


## 2.32.1

`2022-07-01`

### 🐛 问题修复

- 修复 `modelValue` 为 0 时, 导致状态不被选中 ([#1370](https://github.com/arco-design/arco-design-vue/pull/1370))


## 2.29.1

`2022-06-02`

### 💎 功能优化

- 搜索模式下组件失焦后会默认清空输入值 ([#1232](https://github.com/arco-design/arco-design-vue/pull/1232))


## 2.29.0

`2022-05-27`

### 🆕 新增功能

- 默认支持通过标签删除已选项 ([#1206](https://github.com/arco-design/arco-design-vue/pull/1206))

### 🐛 问题修复

- 修复设置了 field-names 后拖拽失效的问题 ([#1207](https://github.com/arco-design/arco-design-vue/pull/1207))


## 2.27.0

`2022-05-13`

### 🆕 新增功能

- 新增属性 `seletable`，支持自定义可选节点 ([#1119](https://github.com/arco-design/arco-design-vue/pull/1119))


## 2.23.0

`2022-04-08`

### 🐛 问题修复

- 当 label-in-value 为 true 的时候，事件 change 的入参错误 ([#939](https://github.com/arco-design/arco-design-vue/pull/939))


## 2.22.0

`2022-04-01`

### 🆕 新增功能

- 新增参数 `fallback-option` ([#894](https://github.com/arco-design/arco-design-vue/pull/894))

### 🐛 问题修复

- 修复  null 值显示为空的问题 ([#916](https://github.com/arco-design/arco-design-vue/pull/916))


## 2.21.0

`2022-03-25`

### 🐛 问题修复

- 修复 `max-tags` 参数名错误的问题，应为 `max-tag-count` ([#873](https://github.com/arco-design/arco-design-vue/pull/873))


## 2.11.0

`2021-12-17`

### 🐛 问题修复

- 修复设置 key 字段名的时候搜索失效的问题 ([#405](https://github.com/arco-design/arco-design-vue/pull/405))


## 2.5.0

`2021-11-18`

### 🐛 问题修复

- 无法选中 key 为 0 的选项 ([#185](https://github.com/arco-design/arco-design-vue/pull/185))


## 2.1.0

`2021-11-05`

### 💎 功能优化

- 选项的可点击范围默认占满一行 ([#90](https://github.com/arco-design/arco-design-vue/pull/90))

