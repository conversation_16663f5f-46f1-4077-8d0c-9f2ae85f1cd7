```yaml
changelog: true
```

## 2.55.0

`2024-03-15`

### 🆕 Feature

- Added `input-value` related properties ([#3024](https://github.com/arco-design/arco-design-vue/pull/3024))


## 2.49.1

`2023-07-24`

### 🐛 BugFix

- Fix the problem that the default value of the `border` attribute is wrong ([#2580](https://github.com/arco-design/arco-design-vue/pull/2580))


## 2.49.0

`2023-07-21`

### 🆕 Feature

- Add header and footer visibility in empty data state ([#2573](https://github.com/arco-design/arco-design-vue/pull/2573))

### 🐛 BugFix

- Fix the problem that the border attribute is invalid ([#2568](https://github.com/arco-design/arco-design-vue/pull/2568))


## 2.47.1

`2023-06-09`

### 🐛 BugFix

- Fix the problem that the search content under the virtual list cannot be selected ([#2488](https://github.com/arco-design/arco-design-vue/pull/2488))


## 2.47.0

`2023-06-02`

### 🆕 Feature

- Add header and footer slots ([#2417](https://github.com/arco-design/arco-design-vue/pull/2417))


## 2.39.1

`2022-11-25`

### 🆎 TypeScript

- update the type of trigger-props ([#1885](https://github.com/arco-design/arco-design-vue/pull/1885))


## 2.39.0

`2022-11-18`

### 🆕 Feature

- Replace the virtual scrollbar component and add the scrollbar property ([#1872](https://github.com/arco-design/arco-design-vue/pull/1872))


## 2.32.1

`2022-07-01`

### 🐛 BugFix

- Fix the problem of selected value in case of modelValue is 0 ([#1370](https://github.com/arco-design/arco-design-vue/pull/1370))


## 2.29.1

`2022-06-02`

### 💎 Enhancement

- When the component is out of focus in search mode, the input value will be cleared by default ([#1232](https://github.com/arco-design/arco-design-vue/pull/1232))


## 2.29.0

`2022-05-27`

### 🆕 Feature

- Default support to delete selected options by tag ([#1206](https://github.com/arco-design/arco-design-vue/pull/1206))

### 🐛 BugFix

- fixed the problem that drag and drop fails after setting fieldNames ([#1207](https://github.com/arco-design/arco-design-vue/pull/1207))


## 2.27.0

`2022-05-13`

### 🆕 Feature

- Added property `seletable` to support custom selectable nodes ([#1119](https://github.com/arco-design/arco-design-vue/pull/1119))


## 2.23.0

`2022-04-08`

### 🐛 BugFix

- When the prop label-in-value is true, the actual parameter value of the event change is wrong ([#939](https://github.com/arco-design/arco-design-vue/pull/939))


## 2.22.0

`2022-04-01`

### 🆕 Feature

- add property `fallback-option` ([#894](https://github.com/arco-design/arco-design-vue/pull/894))

### 🐛 BugFix

- Fix the problem that the null value is displayed as empty ([#916](https://github.com/arco-design/arco-design-vue/pull/916))


## 2.21.0

`2022-03-25`

### 🐛 BugFix

- Fix `max-tags` parameter name is wrong, it should be `max-tag-count` ([#873](https://github.com/arco-design/arco-design-vue/pull/873))


## 2.11.0

`2021-12-17`

### 🐛 BugFix

- fix the problem that search is invalid when rename the key field ([#405](https://github.com/arco-design/arco-design-vue/pull/405))


## 2.5.0

`2021-11-18`

### 🐛 BugFix

- cannot select the option with key 0 ([#185](https://github.com/arco-design/arco-design-vue/pull/185))


## 2.1.0

`2021-11-05`

### 💎 Enhancement

- The clickable range of options occupies the entire row by default ([#90](https://github.com/arco-design/arco-design-vue/pull/90))

