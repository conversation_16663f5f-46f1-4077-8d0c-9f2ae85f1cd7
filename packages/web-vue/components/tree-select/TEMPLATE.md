## zh-CN
```yaml
meta:
  type: 组件
  category: 数据输入
title: 树选择 TreeSelect
description: 可以对树形结构数据进行选择。
```
---
## en-US
```yaml
meta:
  type: Component
  category: Data Entry
title: TreeSelect
description: The tree structure data can be selected.
```
---

@import ./__demo__/basic.md

@import ./__demo__/label-in-value.md

@import ./__demo__/control.md

@import ./__demo__/load-more.md

@import ./__demo__/search.md

@import ./__demo__/search-remote.md

@import ./__demo__/size.md

@import ./__demo__/dropdown-slots.md

@import ./__demo__/trigger-element.md

@import ./__demo__/multiple.md

@import ./__demo__/checkable.md

@import ./__demo__/checked-strategy.md

@import ./__demo__/popup-visible.md

@import ./__demo__/field-names.md

@import ./__demo__/virtual.md

@import ./__demo__/fallback.md

## API

%%API(tree-select.vue)%%
