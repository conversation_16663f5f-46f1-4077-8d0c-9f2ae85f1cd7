@import '../../../style/theme/index.less';
@import '../../../input/style/token.less';

@picker-size-mini: @size-mini;
@picker-size-small: @size-small;
@picker-size-default: @size-default;
@picker-size-large: @size-large;

@picker-size-mini-font-size-text: @font-size-body-1;
@picker-size-small-font-size-text: @font-size-body-3;
@picker-size-default-font-size-text: @font-size-body-3;
@picker-size-large-font-size-text: @font-size-body-3;

@picker-input-border-radius: @radius-small;

@picker-color-shadow_focus: var(
  ~'@{arco-cssvars-prefix}-color-primary-light-2'
);
@picker-size-shadow_focus: @shadow-distance-none;
@picker-color-shadow_error_focus: var(
  ~'@{arco-cssvars-prefix}-color-danger-light-2'
);
@picker-size-shadow_error_focus: @shadow-distance-none;

@picker-color-bg: var(~'@{arco-cssvars-prefix}-color-fill-2');
@picker-color-bg_hover: var(~'@{arco-cssvars-prefix}-color-fill-3');
@picker-color-bg_focus: var(~'@{arco-cssvars-prefix}-color-bg-2');
@picker-color-bg_disabled: var(~'@{arco-cssvars-prefix}-color-fill-2');
@picker-color-bg_error: var(~'@{arco-cssvars-prefix}-color-danger-light-1');
@picker-color-bg_error_hover: var(
  ~'@{arco-cssvars-prefix}-color-danger-light-2'
);
@picker-color-bg_error_focus: var(~'@{arco-cssvars-prefix}-color-bg-2');

@picker-color-border: @color-transparent;
@picker-color-border_hover: @color-transparent;
@picker-color-border_focus: @color-primary-6;
@picker-color-border_disabled: @color-transparent;
@picker-color-border_error: @color-transparent;
@picker-color-border_error_hover: @color-transparent;
@picker-color-border_error_focus: @color-danger-6;

@picker-color-placeholder: var(~'@{arco-cssvars-prefix}-color-text-3');
@picker-color-placeholder_disabled: var(~'@{arco-cssvars-prefix}-color-text-4');
@picker-color-text: var(~'@{arco-cssvars-prefix}-color-text-2');
@picker-color-text_disabled: var(~'@{arco-cssvars-prefix}-color-text-4');
@picker-color-icon: var(~'@{arco-cssvars-prefix}-color-text-2');
@picker-color-icon_disabled: var(~'@{arco-cssvars-prefix}-color-text-4');
@picker-color-separator: var(~'@{arco-cssvars-prefix}-color-text-3');
@picker-color-separator_disabled: var(~'@{arco-cssvars-prefix}-color-text-4');

@picker-range-color-bg-input_focus: var(~'@{arco-cssvars-prefix}-color-fill-2');
@input-color-text_disabled: var(~'@{arco-cssvars-prefix}-color-text-4');

// not in design lab
@picker-padding-horizontal: @spacing-2; // 4px
@picker-input-padding-horizontal: @input-padding-horizontal -
  @picker-padding-horizontal; // 8px
