// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<form> demo: render [async] correctly 1`] = `
"<form class=\\"arco-form arco-form-layout-horizontal arco-form-size-medium\\" style=\\"width: 600px;\\">
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Username
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your username...\\" value=\\"\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Post
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"post\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your post...\\" value=\\"\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"isRead\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><label class=\\"arco-checkbox\\"><input type=\\"checkbox\\" class=\\"arco-checkbox-target\\" value=\\"false\\"><span class=\\"arco-icon-hover arco-checkbox-icon-hover\\"><div class=\\"arco-checkbox-icon\\"><!----></div></span><span class=\\"arco-checkbox-label\\"> I have read the manual </span></label></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
            <!--v-if-->Set Status
          </button></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
</form>
 {
 \\"name\\": \\"\\",
 \\"post\\": \\"\\",
 \\"isRead\\": false
 }"
`;

exports[`<form> demo: render [auto-width] correctly 1`] = `
"<form class=\\"arco-form arco-form-layout-horizontal arco-form-size-medium arco-form-auto-label-width\\" style=\\"width: 600px;\\">
  <div class=\\"arco-row arco-row-nowrap arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col arco-form-item-label-col-flex\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Username
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-form-item-wrapper-col\\" style=\\"flex-basis: auto;\\" id=\\"name\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your username...\\" value=\\"\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-nowrap arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col arco-form-item-label-col-flex\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Post
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-form-item-wrapper-col\\" style=\\"flex-basis: auto;\\" id=\\"post\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your post...\\" value=\\"\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-nowrap arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col arco-form-item-label-col-flex\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-form-item-wrapper-col\\" style=\\"flex-basis: auto;\\" id=\\"isRead\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><label class=\\"arco-checkbox\\"><input type=\\"checkbox\\" class=\\"arco-checkbox-target\\" value=\\"false\\"><span class=\\"arco-icon-hover arco-checkbox-icon-hover\\"><div class=\\"arco-checkbox-icon\\"><!----></div></span><span class=\\"arco-checkbox-label\\"> I have read the manual </span></label></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-nowrap arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col arco-form-item-label-col-flex\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-form-item-wrapper-col\\" style=\\"flex-basis: auto;\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"submit\\">
            <!--v-if-->Submit
          </button></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
</form>
 {
 \\"name\\": \\"\\",
 \\"post\\": \\"\\",
 \\"isRead\\": false
 }"
`;

exports[`<form> demo: render [basic] correctly 1`] = `
"<form class=\\"arco-form arco-form-layout-horizontal arco-form-size-medium\\" style=\\"width: 600px;\\">
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Username<svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-question-circle arco-form-item-label-tooltip\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
          <path d=\\"M42 24c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path>
          <path d=\\"M24.006 31v4.008m0-6.008L24 28c0-3 3-4 4.78-6.402C30.558 19.195 28.288 15 23.987 15c-4.014 0-5.382 2.548-5.388 4.514v.465\\"></path>
        </svg>
        <!---->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your username...\\" value=\\"\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Post
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"post\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your post...\\" value=\\"\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"isRead\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><label class=\\"arco-checkbox\\"><input type=\\"checkbox\\" class=\\"arco-checkbox-target\\" value=\\"false\\"><span class=\\"arco-icon-hover arco-checkbox-icon-hover\\"><div class=\\"arco-checkbox-icon\\"><!----></div></span><span class=\\"arco-checkbox-label\\"> I have read the manual </span></label></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"submit\\">
            <!--v-if-->Submit
          </button></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
</form>
 {
 \\"name\\": \\"\\",
 \\"post\\": \\"\\",
 \\"isRead\\": false
 }"
`;

exports[`<form> demo: render [custom] correctly 1`] = `
"<div class=\\"arco-space arco-space-horizontal arco-space-align-center\\" style=\\"margin-bottom: 20px;\\">
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-right: 8px;\\"><button type=\\"button\\" role=\\"switch\\" aria-checked=\\"false\\" class=\\"arco-switch arco-switch-type-circle\\"><span class=\\"arco-switch-handle\\"><span class=\\"arco-switch-handle-icon\\"></span></span><!--  prettier-ignore  -->
      <!--v-if-->
    </button></div>
  <!---->
  <div class=\\"arco-space-item\\"> Disabled: false</div>
</div>
<form class=\\"arco-form arco-form-layout-horizontal arco-form-size-medium\\" style=\\"width: 600px;\\">
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>Username
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><input modelvalue=\\"\\" placeholder=\\"please enter your username...\\"></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
</form>"
`;

exports[`<form> demo: render [disabled] correctly 1`] = `
"<form class=\\"arco-form arco-form-layout-horizontal arco-form-size-medium\\" style=\\"width: 600px;\\">
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Username
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper arco-input-disabled\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your username...\\" disabled=\\"\\" value=\\"\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Post
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"post\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper arco-input-disabled\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your post...\\" disabled=\\"\\" value=\\"\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"isRead\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><label aria-disabled=\\"true\\" class=\\"arco-checkbox arco-checkbox-disabled\\"><input type=\\"checkbox\\" class=\\"arco-checkbox-target\\" disabled=\\"\\" value=\\"false\\"><span class=\\"arco-icon-hover arco-icon-hover-disabled arco-checkbox-icon-hover\\"><div class=\\"arco-checkbox-icon\\"><!----></div></span><span class=\\"arco-checkbox-label\\"> I have read the manual </span></label></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal arco-btn-disabled\\" type=\\"button\\" disabled=\\"\\">
            <!--v-if-->Submit
          </button></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
</form>
 {
 \\"name\\": \\"\\",
 \\"post\\": \\"\\",
 \\"isRead\\": false
 }"
`;

exports[`<form> demo: render [dynamic] correctly 1`] = `
"<form class=\\"arco-form arco-form-layout-horizontal arco-form-size-medium\\" style=\\"width: 600px;\\">
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Username
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your username...\\" value=\\"\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Post-0
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"posts_0_value\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your post...\\" value=\\"\\"><!----><!----></span><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\" style=\\"margin-left: 10px;\\">
            <!--v-if-->Delete
          </button></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
</form>
<div><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
    <!--v-if-->Add Post
  </button></div>
 {
 \\"name\\": \\"\\",
 \\"posts\\": [
 {
 \\"value\\": \\"\\"
 }
 ]
 }"
`;

exports[`<form> demo: render [extra] correctly 1`] = `
"<form class=\\"arco-form arco-form-layout-horizontal arco-form-size-medium\\" style=\\"width: 600px;\\">
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>Username
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your username...\\" value=\\"\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <div class=\\"arco-form-item-extra\\">
        <div>Used to login</div>
      </div>
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal arco-form-item-has-help\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>Post
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"post\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your post...\\" value=\\"\\"><!----><!----></span></div>
      </div>
      <transition-stub name=\\"form-blink\\" appear=\\"true\\" persisted=\\"false\\" css=\\"true\\">
        <div class=\\"arco-form-item-message arco-form-item-message-help\\">
          <div>Custom valitae message</div>
        </div>
      </transition-stub>
      <div class=\\"arco-form-item-extra\\">
        <div>Used to login</div>
      </div>
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"isRead\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><label class=\\"arco-checkbox\\"><input type=\\"checkbox\\" class=\\"arco-checkbox-target\\" value=\\"false\\"><span class=\\"arco-icon-hover arco-checkbox-icon-hover\\"><div class=\\"arco-checkbox-icon\\"><!----></div></span><span class=\\"arco-checkbox-label\\"> I have read the manual </span></label></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
</form>
 {
 \\"name\\": \\"\\",
 \\"post\\": \\"\\",
 \\"isRead\\": false
 }"
`;

exports[`<form> demo: render [grid] correctly 1`] = `
"<form class=\\"arco-form arco-form-layout-horizontal arco-form-size-medium\\">
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start\\" style=\\"margin-left: -8px; margin-right: -8px;\\">
    <div class=\\"arco-col arco-col-8\\" style=\\"padding-left: 8px; padding-right: 8px;\\">
      <div class=\\"arco-row arco-row-nowrap arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
        <div class=\\"arco-col arco-form-item-label-col arco-form-item-label-col-flex\\" style=\\"flex: 0 0 100px;\\"><label class=\\"arco-form-item-label\\">
            <!--v-if-->Value 1
            <!--v-if-->
            <!--v-if-->
          </label></div>
        <div class=\\"arco-col arco-form-item-wrapper-col\\" style=\\"flex-basis: auto;\\" id=\\"value1\\">
          <div class=\\"arco-form-item-content-wrapper\\">
            <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter...\\" value=\\"\\"><!----><!----></span></div>
          </div>
          <!--v-if-->
          <!--v-if-->
        </div>
      </div>
    </div>
    <div class=\\"arco-col arco-col-8\\" style=\\"padding-left: 8px; padding-right: 8px;\\">
      <div class=\\"arco-row arco-row-nowrap arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
        <div class=\\"arco-col arco-form-item-label-col arco-form-item-label-col-flex\\" style=\\"flex: 0 0 80px;\\"><label class=\\"arco-form-item-label\\">
            <!--v-if-->Value 2
            <!--v-if-->
            <!--v-if-->
          </label></div>
        <div class=\\"arco-col arco-form-item-wrapper-col\\" style=\\"flex-basis: auto;\\" id=\\"value2\\">
          <div class=\\"arco-form-item-content-wrapper\\">
            <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter...\\" value=\\"\\"><!----><!----></span></div>
          </div>
          <!--v-if-->
          <!--v-if-->
        </div>
      </div>
    </div>
    <div class=\\"arco-col arco-col-8\\" style=\\"padding-left: 8px; padding-right: 8px;\\">
      <div class=\\"arco-row arco-row-nowrap arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
        <div class=\\"arco-col arco-form-item-label-col arco-form-item-label-col-flex\\" style=\\"flex: 0 0 80px;\\"><label class=\\"arco-form-item-label\\">
            <!--v-if-->Value 3
            <!--v-if-->
            <!--v-if-->
          </label></div>
        <div class=\\"arco-col arco-form-item-wrapper-col\\" style=\\"flex-basis: auto;\\" id=\\"value3\\">
          <div class=\\"arco-form-item-content-wrapper\\">
            <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter...\\" value=\\"\\"><!----><!----></span></div>
          </div>
          <!--v-if-->
          <!--v-if-->
        </div>
      </div>
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start\\" style=\\"margin-left: -8px; margin-right: -8px;\\">
    <div class=\\"arco-col arco-col-16\\" style=\\"padding-left: 8px; padding-right: 8px;\\">
      <div class=\\"arco-row arco-row-nowrap arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
        <div class=\\"arco-col arco-form-item-label-col arco-form-item-label-col-flex\\" style=\\"flex: 0 0 100px;\\"><label class=\\"arco-form-item-label\\">
            <!--v-if-->Value 4
            <!--v-if-->
            <!--v-if-->
          </label></div>
        <div class=\\"arco-col arco-form-item-wrapper-col\\" style=\\"flex-basis: auto;\\" id=\\"value4\\">
          <div class=\\"arco-form-item-content-wrapper\\">
            <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter...\\" value=\\"\\"><!----><!----></span></div>
          </div>
          <!--v-if-->
          <!--v-if-->
        </div>
      </div>
    </div>
    <div class=\\"arco-col arco-col-8\\" style=\\"padding-left: 8px; padding-right: 8px;\\">
      <div class=\\"arco-row arco-row-nowrap arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
        <div class=\\"arco-col arco-form-item-label-col arco-form-item-label-col-flex\\" style=\\"flex: 0 0 80px;\\"><label class=\\"arco-form-item-label\\">
            <!--v-if-->Value 5
            <!--v-if-->
            <!--v-if-->
          </label></div>
        <div class=\\"arco-col arco-form-item-wrapper-col\\" style=\\"flex-basis: auto;\\" id=\\"value5\\">
          <div class=\\"arco-form-item-content-wrapper\\">
            <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter...\\" value=\\"\\"><!----><!----></span></div>
          </div>
          <!--v-if-->
          <!--v-if-->
        </div>
      </div>
    </div>
  </div>
</form>
 {
 \\"value1\\": \\"\\",
 \\"value2\\": \\"\\",
 \\"value3\\": \\"\\",
 \\"value4\\": \\"\\",
 \\"value5\\": \\"\\"
 }"
`;

exports[`<form> demo: render [layout] correctly 1`] = `
"<div class=\\"arco-space arco-space-vertical\\" style=\\"width: 600px;\\">
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 24px;\\"><span class=\\"arco-radio-group-button arco-radio-group-size-medium arco-radio-group-direction-horizontal\\"><label class=\\"arco-radio-button arco-radio-checked\\"><input type=\\"radio\\" checked=\\"\\" class=\\"arco-radio-target\\" value=\\"horizontal\\"><span class=\\"arco-radio-button-content\\">horizontal</span></label><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"vertical\\"><span class=\\"arco-radio-button-content\\">vertical</span></label><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"inline\\"><span class=\\"arco-radio-button-content\\">inline</span></label></span></div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 24px;\\">
    <form class=\\"arco-form arco-form-layout-horizontal arco-form-size-medium\\">
      <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
        <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
            <!--v-if-->Username
            <!--v-if-->
            <!--v-if-->
          </label></div>
        <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name\\">
          <div class=\\"arco-form-item-content-wrapper\\">
            <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your username...\\" value=\\"\\"><!----><!----></span></div>
          </div>
          <!--v-if-->
          <!--v-if-->
        </div>
      </div>
      <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
        <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
            <!--v-if-->Post
            <!--v-if-->
            <!--v-if-->
          </label></div>
        <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"post\\">
          <div class=\\"arco-form-item-content-wrapper\\">
            <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your post...\\" value=\\"\\"><!----><!----></span></div>
          </div>
          <!--v-if-->
          <!--v-if-->
        </div>
      </div>
      <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
        <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
            <!--v-if-->
            <!--v-if-->
            <!--v-if-->
          </label></div>
        <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"isRead\\">
          <div class=\\"arco-form-item-content-wrapper\\">
            <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><label class=\\"arco-checkbox\\"><input type=\\"checkbox\\" class=\\"arco-checkbox-target\\" value=\\"false\\"><span class=\\"arco-icon-hover arco-checkbox-icon-hover\\"><div class=\\"arco-checkbox-icon\\"><!----></div></span><span class=\\"arco-checkbox-label\\"> I have read the manual </span></label></div>
          </div>
          <!--v-if-->
          <!--v-if-->
        </div>
      </div>
      <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
        <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
            <!--v-if-->
            <!--v-if-->
            <!--v-if-->
          </label></div>
        <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\">
          <div class=\\"arco-form-item-content-wrapper\\">
            <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
                <!--v-if-->Submit
              </button></div>
          </div>
          <!--v-if-->
          <!--v-if-->
        </div>
      </div>
    </form>
  </div>
  <!---->
  <div class=\\"arco-space-item\\">
    <div>{
      \\"name\\": \\"\\",
      \\"post\\": \\"\\",
      \\"isRead\\": false
      }</div>
  </div>
</div>"
`;

exports[`<form> demo: render [nest] correctly 1`] = `
"<form class=\\"arco-form arco-form-layout-horizontal arco-form-size-medium\\" style=\\"width: 600px;\\">
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Username
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content\\">
          <div class=\\"arco-row arco-row-align-start arco-row-justify-start\\" style=\\"margin-left: -4px; margin-right: -4px;\\">
            <div class=\\"arco-col arco-col-12\\" style=\\"padding-left: 4px; padding-right: 4px;\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your firstname...\\" value=\\"\\"><!----><!----></span></div>
            <div class=\\"arco-col arco-col-12\\" style=\\"padding-left: 4px; padding-right: 4px;\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your lastname...\\" value=\\"\\"><!----><!----></span></div>
          </div>
        </div>
      </div>
      <!--v-if-->
      <div class=\\"arco-form-item-extra\\">Show error message together</div>
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Username
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content\\">
          <div class=\\"arco-row arco-row-align-start arco-row-justify-start\\" style=\\"margin-left: -4px; margin-right: -4px;\\">
            <div class=\\"arco-col arco-col-12\\" style=\\"padding-left: 4px; padding-right: 4px;\\">
              <div class=\\"arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
                <!--v-if-->
                <div class=\\"arco-form-item-wrapper-col\\" id=\\"separate_firstname\\">
                  <div class=\\"arco-form-item-content-wrapper\\">
                    <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your firstname...\\" value=\\"\\"><!----><!----></span></div>
                  </div>
                  <!--v-if-->
                  <div class=\\"arco-form-item-extra\\">Show error message separate</div>
                </div>
              </div>
            </div>
            <div class=\\"arco-col arco-col-12\\" style=\\"padding-left: 4px; padding-right: 4px;\\">
              <div class=\\"arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
                <!--v-if-->
                <div class=\\"arco-form-item-wrapper-col\\" id=\\"separate_lastname\\">
                  <div class=\\"arco-form-item-content-wrapper\\">
                    <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your lastname...\\" value=\\"\\"><!----><!----></span></div>
                  </div>
                  <!--v-if-->
                  <!--v-if-->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Posts
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content\\">
          <div class=\\"arco-space arco-space-vertical arco-space-fill\\">
            <!---->
            <div class=\\"arco-space-item\\" style=\\"margin-bottom: 8px;\\">
              <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
                <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
                    <!--v-if-->Post1
                    <!--v-if-->
                    <!--v-if-->
                  </label></div>
                <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"posts_post1\\">
                  <div class=\\"arco-form-item-content-wrapper\\">
                    <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your post...\\" value=\\"\\"><!----><!----></span></div>
                  </div>
                  <!--v-if-->
                  <!--v-if-->
                </div>
              </div>
            </div>
            <!---->
            <div class=\\"arco-space-item\\">
              <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
                <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
                    <!--v-if-->Post2
                    <!--v-if-->
                    <!--v-if-->
                  </label></div>
                <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"posts_post2\\">
                  <div class=\\"arco-form-item-content-wrapper\\">
                    <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your post...\\" value=\\"\\"><!----><!----></span></div>
                  </div>
                  <!--v-if-->
                  <!--v-if-->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"isRead\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><label class=\\"arco-checkbox\\"><input type=\\"checkbox\\" class=\\"arco-checkbox-target\\" value=\\"false\\"><span class=\\"arco-icon-hover arco-checkbox-icon-hover\\"><div class=\\"arco-checkbox-icon\\"><!----></div></span><span class=\\"arco-checkbox-label\\"> I have read the manual </span></label></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
</form>
 {
 \\"together\\": {
 \\"firstname\\": \\"\\",
 \\"lastname\\": \\"\\"
 },
 \\"separate\\": {
 \\"firstname\\": \\"\\",
 \\"lastname\\": \\"\\"
 },
 \\"posts\\": {
 \\"post1\\": \\"\\",
 \\"post2\\": \\"\\"
 },
 \\"isRead\\": false
 }"
`;

exports[`<form> demo: render [scroll] correctly 1`] = `
"<div class=\\"arco-space arco-space-horizontal arco-space-align-center\\">
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-right: 8px;\\"><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
      <!--v-if-->Submit
    </button></div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-right: 8px;\\"><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
      <!--v-if-->Reset
    </button></div>
  <!---->
  <div class=\\"arco-space-item\\"><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
      <!--v-if-->Scroll to the last field
    </button></div>
</div>
<form class=\\"arco-form arco-form-layout-horizontal arco-form-size-medium\\" style=\\"width: 500px; height: 300px; margin-top: 20px; padding-right: 16px; overflow: auto;\\">
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>user0
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name0\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" value=\\"0\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>user1
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name1\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" value=\\"1\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>user2
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name2\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" value=\\"2\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>user3
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name3\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" value=\\"3\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>user4
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name4\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" value=\\"4\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>user5
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name5\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" value=\\"5\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>user6
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name6\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" value=\\"6\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>user7
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name7\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" value=\\"\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>user8
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name8\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" value=\\"8\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>user9
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name9\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" value=\\"9\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>user10
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name10\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" value=\\"10\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>user11
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name11\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" value=\\"11\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>user12
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name12\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" value=\\"12\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>user13
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name13\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" value=\\"13\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>user14
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name14\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" value=\\"14\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>user15
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name15\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" value=\\"15\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>user16
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name16\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" value=\\"16\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>user17
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name17\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" value=\\"17\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>user18
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name18\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" value=\\"18\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>user19
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name19\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" value=\\"19\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
</form>"
`;

exports[`<form> demo: render [status] correctly 1`] = `
"<div class=\\"arco-space arco-space-vertical\\">
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 24px;\\"><span class=\\"arco-radio-group-button arco-radio-group-size-medium arco-radio-group-direction-horizontal\\"><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"validating\\"><span class=\\"arco-radio-button-content\\">validating</span></label><label class=\\"arco-radio-button arco-radio-checked\\"><input type=\\"radio\\" checked=\\"\\" class=\\"arco-radio-target\\" value=\\"success\\"><span class=\\"arco-radio-button-content\\">success</span></label><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"error\\"><span class=\\"arco-radio-button-content\\">error</span></label><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"warning\\"><span class=\\"arco-radio-button-content\\">warning</span></label></span></div>
  <!---->
  <div class=\\"arco-space-item\\"><span class=\\"arco-radio-group-button arco-radio-group-size-medium arco-radio-group-direction-horizontal\\"><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"mini\\"><span class=\\"arco-radio-button-content\\">mini</span></label><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"small\\"><span class=\\"arco-radio-button-content\\">small</span></label><label class=\\"arco-radio-button arco-radio-checked\\"><input type=\\"radio\\" checked=\\"\\" class=\\"arco-radio-target\\" value=\\"medium\\"><span class=\\"arco-radio-button-content\\">medium</span></label><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"large\\"><span class=\\"arco-radio-button-content\\">large</span></label></span></div>
</div>
<form class=\\"arco-form arco-form-layout-horizontal arco-form-size-medium\\" style=\\"width: 600px; margin-top: 20px;\\">
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal arco-form-item-status-success arco-form-item-has-help\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Username
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your username...\\" value=\\"\\"><!----><span class=\\"arco-input-suffix arco-input-suffix-has-feedback\\"><!----><!----><span class=\\"arco-feedback-icon arco-feedback-icon-status-success\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-check-circle-fill\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path fill-rule=\\"evenodd\\" clip-rule=\\"evenodd\\" d=\\"M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Zm10.207-24.379a1 1 0 0 0 0-1.414l-1.414-1.414a1 1 0 0 0-1.414 0L22 26.172l-4.878-4.88a1 1 0 0 0-1.415 0l-1.414 1.415a1 1 0 0 0 0 1.414l7 7a1 1 0 0 0 1.414 0l11.5-11.5Z\\" fill=\\"currentColor\\" stroke=\\"none\\"></path></svg></span></span></span></div>
      </div>
      <transition-stub name=\\"form-blink\\" appear=\\"true\\" persisted=\\"false\\" css=\\"true\\">
        <div class=\\"arco-form-item-message arco-form-item-message-help\\">This is custom message</div>
      </transition-stub>
      <div class=\\"arco-form-item-extra\\">This is extra text</div>
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal arco-form-item-status-success arco-form-item-has-help\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Post
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"post\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper arco-input-number arco-input-number-mode-embed arco-input-number-size-medium\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your post...\\" role=\\"spinbutton\\" aria-valuemax=\\"Infinity\\" aria-valuemin=\\"-Infinity\\" aria-valuenow=\\"\\" value=\\"\\"><!----><span class=\\"arco-input-suffix arco-input-suffix-has-feedback\\"><!----><!----><div class=\\"arco-input-number-step\\"><button class=\\"arco-input-number-step-button\\" type=\\"button\\" tabindex=\\"-1\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-up\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 30.557 24.043 15 8.487 30.557\\"></path></svg></button><button class=\\"arco-input-number-step-button\\" type=\\"button\\" tabindex=\\"-1\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></button></div><span class=\\"arco-feedback-icon arco-feedback-icon-status-success\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-check-circle-fill\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path fill-rule=\\"evenodd\\" clip-rule=\\"evenodd\\" d=\\"M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Zm10.207-24.379a1 1 0 0 0 0-1.414l-1.414-1.414a1 1 0 0 0-1.414 0L22 26.172l-4.878-4.88a1 1 0 0 0-1.415 0l-1.414 1.415a1 1 0 0 0 0 1.414l7 7a1 1 0 0 0 1.414 0l11.5-11.5Z\\" fill=\\"currentColor\\" stroke=\\"none\\"></path></svg></span></span></span></div>
      </div>
      <transition-stub name=\\"form-blink\\" appear=\\"true\\" persisted=\\"false\\" css=\\"true\\">
        <div class=\\"arco-form-item-message arco-form-item-message-help\\">This is custom message</div>
      </transition-stub>
      <div class=\\"arco-form-item-extra\\">This is extra text</div>
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal arco-form-item-status-success arco-form-item-has-help\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Tags
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"tags\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-tag arco-input-tag-size-medium arco-input-tag-has-tag arco-input-tag-has-suffix\\"><span class=\\"arco-input-tag-mirror\\"></span>
          <!---->
          <transition-group-stub tag=\\"span\\" name=\\"input-tag-zoom\\" appear=\\"false\\" persisted=\\"false\\" css=\\"true\\" class=\\"arco-input-tag-inner\\"><span class=\\"arco-tag arco-tag-size-medium arco-tag-checked arco-input-tag-tag\\"><!--v-if-->tag1<span class=\\"arco-icon-hover arco-tag-icon-hover arco-tag-close-btn\\" role=\\"button\\" aria-label=\\"Close\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span>
            <!--v-if--></span><input class=\\"arco-input-tag-input\\" style=\\"width: 12px;\\">
          </transition-group-stub>
          <!----><span class=\\"arco-input-tag-suffix\\"><!----><span class=\\"arco-feedback-icon arco-feedback-icon-status-success\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-check-circle-fill\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path fill-rule=\\"evenodd\\" clip-rule=\\"evenodd\\" d=\\"M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Zm10.207-24.379a1 1 0 0 0 0-1.414l-1.414-1.414a1 1 0 0 0-1.414 0L22 26.172l-4.878-4.88a1 1 0 0 0-1.415 0l-1.414 1.415a1 1 0 0 0 0 1.414l7 7a1 1 0 0 0 1.414 0l11.5-11.5Z\\" fill=\\"currentColor\\" stroke=\\"none\\"></path></svg></span></span></span>
        </div>
      </div>
      <transition-stub name=\\"form-blink\\" appear=\\"true\\" persisted=\\"false\\" css=\\"true\\">
        <div class=\\"arco-form-item-message arco-form-item-message-help\\">This is custom message</div>
      </transition-stub>
      <div class=\\"arco-form-item-extra\\">This is extra text</div>
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal arco-form-item-status-success\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Section
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"section\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-select-view-single arco-select arco-select-view arco-select-view-size-medium\\" title=\\"\\"><!----><input class=\\"arco-select-view-input\\" readonly=\\"\\" placeholder=\\"Please select ...\\" value=\\"\\"><span class=\\"arco-select-view-value arco-select-view-value-hidden\\"><!----></span><span class=\\"arco-select-view-suffix\\"><!----><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down arco-select-view-arrow-icon\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></span><span class=\\"arco-feedback-icon arco-feedback-icon-status-success\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-check-circle-fill\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path fill-rule=\\"evenodd\\" clip-rule=\\"evenodd\\" d=\\"M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Zm10.207-24.379a1 1 0 0 0 0-1.414l-1.414-1.414a1 1 0 0 0-1.414 0L22 26.172l-4.878-4.88a1 1 0 0 0-1.415 0l-1.414 1.415a1 1 0 0 0 0 1.414l7 7a1 1 0 0 0 1.414 0l11.5-11.5Z\\" fill=\\"currentColor\\" stroke=\\"none\\"></path></svg></span></span></span>
          <!---->
        </div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal arco-form-item-status-success\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->DateRange
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\">
          <div class=\\"arco-picker arco-picker-range arco-picker-size-medium\\" visible=\\"false\\">
            <!--v-if-->
            <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"开始日期\\"></div><span class=\\"arco-picker-separator\\">-</span>
            <div class=\\"arco-picker-input\\"><input placeholder=\\"结束日期\\"></div>
            <div class=\\"arco-picker-suffix\\">
              <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span><span class=\\"arco-feedback-icon arco-feedback-icon-status-success\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-check-circle-fill\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path fill-rule=\\"evenodd\\" clip-rule=\\"evenodd\\" d=\\"M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Zm10.207-24.379a1 1 0 0 0 0-1.414l-1.414-1.414a1 1 0 0 0-1.414 0L22 26.172l-4.878-4.88a1 1 0 0 0-1.415 0l-1.414 1.415a1 1 0 0 0 0 1.414l7 7a1 1 0 0 0 1.414 0l11.5-11.5Z\\" fill=\\"currentColor\\" stroke=\\"none\\"></path></svg></span>
            </div>
          </div>
          <!---->
        </div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal arco-form-item-status-success\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Date
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"date\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\">
          <div class=\\"arco-picker arco-picker-size-medium\\" visible=\\"false\\">
            <!--v-if-->
            <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择日期\\" class=\\"arco-picker-start-time\\"></div>
            <div class=\\"arco-picker-suffix\\">
              <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span><span class=\\"arco-feedback-icon arco-feedback-icon-status-success\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-check-circle-fill\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path fill-rule=\\"evenodd\\" clip-rule=\\"evenodd\\" d=\\"M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Zm10.207-24.379a1 1 0 0 0 0-1.414l-1.414-1.414a1 1 0 0 0-1.414 0L22 26.172l-4.878-4.88a1 1 0 0 0-1.415 0l-1.414 1.415a1 1 0 0 0 0 1.414l7 7a1 1 0 0 0 1.414 0l11.5-11.5Z\\" fill=\\"currentColor\\" stroke=\\"none\\"></path></svg></span>
            </div>
          </div>
          <!---->
        </div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal arco-form-item-status-success\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Time
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"time\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\">
          <div class=\\"arco-picker arco-picker-size-medium\\" visible=\\"false\\" editable=\\"true\\">
            <!--v-if-->
            <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择时间\\" class=\\"arco-picker-start-time\\"></div>
            <div class=\\"arco-picker-suffix\\">
              <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-clock-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M24 14v10h9.5m8.5 0c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path></svg></span><span class=\\"arco-feedback-icon arco-feedback-icon-status-success\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-check-circle-fill\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path fill-rule=\\"evenodd\\" clip-rule=\\"evenodd\\" d=\\"M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Zm10.207-24.379a1 1 0 0 0 0-1.414l-1.414-1.414a1 1 0 0 0-1.414 0L22 26.172l-4.878-4.88a1 1 0 0 0-1.415 0l-1.414 1.415a1 1 0 0 0 0 1.414l7 7a1 1 0 0 0 1.414 0l11.5-11.5Z\\" fill=\\"currentColor\\" stroke=\\"none\\"></path></svg></span>
            </div>
          </div>
          <!---->
        </div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
</form>"
`;

exports[`<form> demo: render [validation] correctly 1`] = `
"<form class=\\"arco-form arco-form-layout-horizontal arco-form-size-medium\\" style=\\"width: 600px;\\">
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Form Size
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"size\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-radio-group-button arco-radio-group-size-medium arco-radio-group-direction-horizontal\\"><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"mini\\"><span class=\\"arco-radio-button-content\\">Mini</span></label><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"small\\"><span class=\\"arco-radio-button-content\\">Small</span></label><label class=\\"arco-radio-button arco-radio-checked\\"><input type=\\"radio\\" checked=\\"\\" class=\\"arco-radio-target\\" value=\\"medium\\"><span class=\\"arco-radio-button-content\\">Medium</span></label><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"large\\"><span class=\\"arco-radio-button-content\\">Large</span></label></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>Username
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your username...\\" value=\\"\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>Age
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"age\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper arco-input-number arco-input-number-mode-embed arco-input-number-size-medium\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your age...\\" role=\\"spinbutton\\" aria-valuemax=\\"Infinity\\" aria-valuemin=\\"-Infinity\\" aria-valuenow=\\"\\" value=\\"\\"><!----><span class=\\"arco-input-suffix\\"><!----><!----><div class=\\"arco-input-number-step\\"><button class=\\"arco-input-number-step-button\\" type=\\"button\\" tabindex=\\"-1\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-up\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 30.557 24.043 15 8.487 30.557\\"></path></svg></button><button class=\\"arco-input-number-step-button\\" type=\\"button\\" tabindex=\\"-1\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></button></div><!----></span></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Section
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"section\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-select-view-single arco-select arco-select-view arco-select-view-size-medium\\" title=\\"\\"><!----><input class=\\"arco-select-view-input\\" readonly=\\"\\" placeholder=\\"Please select ...\\" value=\\"\\"><span class=\\"arco-select-view-value arco-select-view-value-hidden\\"><!----></span><span class=\\"arco-select-view-suffix\\"><!----><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down arco-select-view-arrow-icon\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></span>
          <!----></span></span>
          <!---->
        </div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>Province
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"province\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-select-view-single arco-select-view arco-select-view-size-medium\\" title=\\"Beijing / Haidian\\"><!----><input class=\\"arco-select-view-input arco-select-view-input-hidden\\" readonly=\\"\\" placeholder=\\"Please select ...\\" value=\\"\\"><span class=\\"arco-select-view-value\\">Beijing / Haidian</span><span class=\\"arco-select-view-suffix\\"><span class=\\"arco-icon-hover arco-select-view-clear-btn\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down arco-select-view-arrow-icon\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></span>
          <!----></span></span>
          <!---->
        </div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Options
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"options\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-checkbox-group arco-checkbox-group-direction-horizontal\\"><label aria-disabled=\\"false\\" class=\\"arco-checkbox\\"><input type=\\"checkbox\\" class=\\"arco-checkbox-target\\" value=\\"option one\\"><span class=\\"arco-icon-hover arco-checkbox-icon-hover\\"><div class=\\"arco-checkbox-icon\\"><!----></div></span><span class=\\"arco-checkbox-label\\">Section One</span></label><label aria-disabled=\\"false\\" class=\\"arco-checkbox\\"><input type=\\"checkbox\\" class=\\"arco-checkbox-target\\" value=\\"option two\\"><span class=\\"arco-icon-hover arco-checkbox-icon-hover\\"><div class=\\"arco-checkbox-icon\\"><!----></div></span><span class=\\"arco-checkbox-label\\">Option Two</span></label><label aria-disabled=\\"false\\" class=\\"arco-checkbox\\"><input type=\\"checkbox\\" class=\\"arco-checkbox-target\\" value=\\"option three\\"><span class=\\"arco-icon-hover arco-checkbox-icon-hover\\"><div class=\\"arco-checkbox-icon\\"><!----></div></span><span class=\\"arco-checkbox-label\\">Option Three</span></label><label aria-disabled=\\"false\\" class=\\"arco-checkbox\\"><input type=\\"checkbox\\" class=\\"arco-checkbox-target\\" value=\\"option four\\"><span class=\\"arco-icon-hover arco-checkbox-icon-hover\\"><div class=\\"arco-checkbox-icon\\"><!----></div></span><span class=\\"arco-checkbox-label\\">Option Four</span></label></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Date
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"date\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\">
          <div class=\\"arco-picker arco-picker-size-medium\\" visible=\\"false\\">
            <!--v-if-->
            <div class=\\"arco-picker-input\\"><input placeholder=\\"Please select ...\\" class=\\"arco-picker-start-time\\"></div>
            <div class=\\"arco-picker-suffix\\">
              <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
              <!--v-if-->
            </div>
          </div>
          <!---->
        </div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Time
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"time\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\">
          <div class=\\"arco-picker arco-picker-size-medium\\" visible=\\"false\\" editable=\\"true\\">
            <!--v-if-->
            <div class=\\"arco-picker-input\\"><input placeholder=\\"Please select ...\\" class=\\"arco-picker-start-time\\"></div>
            <div class=\\"arco-picker-suffix\\">
              <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-clock-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M24 14v10h9.5m8.5 0c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path></svg></span>
              <!--v-if-->
            </div>
          </div>
          <!---->
        </div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Radio
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"radio\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-radio-group arco-radio-group-size-medium arco-radio-group-direction-horizontal\\"><label class=\\"arco-radio arco-radio-checked\\"><input type=\\"radio\\" checked=\\"\\" class=\\"arco-radio-target\\" value=\\"radio one\\"><span class=\\"arco-icon-hover arco-icon-hover-disabled arco-radio-icon-hover\\"><span class=\\"arco-radio-icon\\"></span></span><span class=\\"arco-radio-label\\">Radio One</span></label><label class=\\"arco-radio\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"radio two\\"><span class=\\"arco-icon-hover arco-radio-icon-hover\\"><span class=\\"arco-radio-icon\\"></span></span><span class=\\"arco-radio-label\\">Radio Two</span></label></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Slider
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"slider\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\">
          <div class=\\"arco-slider\\">
            <div class=\\"arco-slider-track\\">
              <div class=\\"arco-slider-bar\\" style=\\"left: 0%; right: 50%;\\"></div>
              <!--v-if-->
              <!--v-if-->
              <!--v-if-->
              <!--v-if-->
              <div style=\\"left: 50%;\\" tabindex=\\"0\\" role=\\"slider\\" aria-disabled=\\"false\\" aria-valuemax=\\"10\\" aria-valuemin=\\"0\\" aria-valuenow=\\"5\\" aria-valuetext=\\"5\\" class=\\"arco-slider-btn\\"></div>
              <!---->
            </div>
            <!--v-if-->
          </div>
        </div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Score
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"score\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\">
          <div class=\\"arco-rate\\">
            <div class=\\"arco-rate-character arco-rate-character-full\\" role=\\"radio\\" aria-checked=\\"true\\" aria-setsize=\\"5\\" aria-posinset=\\"1\\">
              <div class=\\"arco-rate-character-left\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-star-fill\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
                  <path d=\\"M22.683 5.415c.568-1.043 2.065-1.043 2.634 0l5.507 10.098a1.5 1.5 0 0 0 1.04.756l11.306 2.117c1.168.219 1.63 1.642.814 2.505l-7.902 8.359a1.5 1.5 0 0 0-.397 1.223l1.48 11.407c.153 1.177-1.058 2.057-2.131 1.548l-10.391-4.933a1.5 1.5 0 0 0-1.287 0l-10.39 4.933c-1.073.51-2.284-.37-2.131-1.548l1.48-11.407a1.5 1.5 0 0 0-.398-1.223L4.015 20.89c-.816-.863-.353-2.286.814-2.505l11.306-2.117a1.5 1.5 0 0 0 1.04-.756l5.508-10.098Z\\" fill=\\"currentColor\\" stroke=\\"none\\"></path>
                </svg></div>
              <div class=\\"arco-rate-character-right\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-star-fill\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
                  <path d=\\"M22.683 5.415c.568-1.043 2.065-1.043 2.634 0l5.507 10.098a1.5 1.5 0 0 0 1.04.756l11.306 2.117c1.168.219 1.63 1.642.814 2.505l-7.902 8.359a1.5 1.5 0 0 0-.397 1.223l1.48 11.407c.153 1.177-1.058 2.057-2.131 1.548l-10.391-4.933a1.5 1.5 0 0 0-1.287 0l-10.39 4.933c-1.073.51-2.284-.37-2.131-1.548l1.48-11.407a1.5 1.5 0 0 0-.398-1.223L4.015 20.89c-.816-.863-.353-2.286.814-2.505l11.306-2.117a1.5 1.5 0 0 0 1.04-.756l5.508-10.098Z\\" fill=\\"currentColor\\" stroke=\\"none\\"></path>
                </svg></div>
            </div>
            <div class=\\"arco-rate-character arco-rate-character-full\\" role=\\"radio\\" aria-checked=\\"true\\" aria-setsize=\\"5\\" aria-posinset=\\"2\\">
              <div class=\\"arco-rate-character-left\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-star-fill\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
                  <path d=\\"M22.683 5.415c.568-1.043 2.065-1.043 2.634 0l5.507 10.098a1.5 1.5 0 0 0 1.04.756l11.306 2.117c1.168.219 1.63 1.642.814 2.505l-7.902 8.359a1.5 1.5 0 0 0-.397 1.223l1.48 11.407c.153 1.177-1.058 2.057-2.131 1.548l-10.391-4.933a1.5 1.5 0 0 0-1.287 0l-10.39 4.933c-1.073.51-2.284-.37-2.131-1.548l1.48-11.407a1.5 1.5 0 0 0-.398-1.223L4.015 20.89c-.816-.863-.353-2.286.814-2.505l11.306-2.117a1.5 1.5 0 0 0 1.04-.756l5.508-10.098Z\\" fill=\\"currentColor\\" stroke=\\"none\\"></path>
                </svg></div>
              <div class=\\"arco-rate-character-right\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-star-fill\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
                  <path d=\\"M22.683 5.415c.568-1.043 2.065-1.043 2.634 0l5.507 10.098a1.5 1.5 0 0 0 1.04.756l11.306 2.117c1.168.219 1.63 1.642.814 2.505l-7.902 8.359a1.5 1.5 0 0 0-.397 1.223l1.48 11.407c.153 1.177-1.058 2.057-2.131 1.548l-10.391-4.933a1.5 1.5 0 0 0-1.287 0l-10.39 4.933c-1.073.51-2.284-.37-2.131-1.548l1.48-11.407a1.5 1.5 0 0 0-.398-1.223L4.015 20.89c-.816-.863-.353-2.286.814-2.505l11.306-2.117a1.5 1.5 0 0 0 1.04-.756l5.508-10.098Z\\" fill=\\"currentColor\\" stroke=\\"none\\"></path>
                </svg></div>
            </div>
            <div class=\\"arco-rate-character arco-rate-character-full\\" role=\\"radio\\" aria-checked=\\"true\\" aria-setsize=\\"5\\" aria-posinset=\\"3\\">
              <div class=\\"arco-rate-character-left\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-star-fill\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
                  <path d=\\"M22.683 5.415c.568-1.043 2.065-1.043 2.634 0l5.507 10.098a1.5 1.5 0 0 0 1.04.756l11.306 2.117c1.168.219 1.63 1.642.814 2.505l-7.902 8.359a1.5 1.5 0 0 0-.397 1.223l1.48 11.407c.153 1.177-1.058 2.057-2.131 1.548l-10.391-4.933a1.5 1.5 0 0 0-1.287 0l-10.39 4.933c-1.073.51-2.284-.37-2.131-1.548l1.48-11.407a1.5 1.5 0 0 0-.398-1.223L4.015 20.89c-.816-.863-.353-2.286.814-2.505l11.306-2.117a1.5 1.5 0 0 0 1.04-.756l5.508-10.098Z\\" fill=\\"currentColor\\" stroke=\\"none\\"></path>
                </svg></div>
              <div class=\\"arco-rate-character-right\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-star-fill\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
                  <path d=\\"M22.683 5.415c.568-1.043 2.065-1.043 2.634 0l5.507 10.098a1.5 1.5 0 0 0 1.04.756l11.306 2.117c1.168.219 1.63 1.642.814 2.505l-7.902 8.359a1.5 1.5 0 0 0-.397 1.223l1.48 11.407c.153 1.177-1.058 2.057-2.131 1.548l-10.391-4.933a1.5 1.5 0 0 0-1.287 0l-10.39 4.933c-1.073.51-2.284-.37-2.131-1.548l1.48-11.407a1.5 1.5 0 0 0-.398-1.223L4.015 20.89c-.816-.863-.353-2.286.814-2.505l11.306-2.117a1.5 1.5 0 0 0 1.04-.756l5.508-10.098Z\\" fill=\\"currentColor\\" stroke=\\"none\\"></path>
                </svg></div>
            </div>
            <div class=\\"arco-rate-character arco-rate-character-full\\" role=\\"radio\\" aria-checked=\\"true\\" aria-setsize=\\"5\\" aria-posinset=\\"4\\">
              <div class=\\"arco-rate-character-left\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-star-fill\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
                  <path d=\\"M22.683 5.415c.568-1.043 2.065-1.043 2.634 0l5.507 10.098a1.5 1.5 0 0 0 1.04.756l11.306 2.117c1.168.219 1.63 1.642.814 2.505l-7.902 8.359a1.5 1.5 0 0 0-.397 1.223l1.48 11.407c.153 1.177-1.058 2.057-2.131 1.548l-10.391-4.933a1.5 1.5 0 0 0-1.287 0l-10.39 4.933c-1.073.51-2.284-.37-2.131-1.548l1.48-11.407a1.5 1.5 0 0 0-.398-1.223L4.015 20.89c-.816-.863-.353-2.286.814-2.505l11.306-2.117a1.5 1.5 0 0 0 1.04-.756l5.508-10.098Z\\" fill=\\"currentColor\\" stroke=\\"none\\"></path>
                </svg></div>
              <div class=\\"arco-rate-character-right\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-star-fill\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
                  <path d=\\"M22.683 5.415c.568-1.043 2.065-1.043 2.634 0l5.507 10.098a1.5 1.5 0 0 0 1.04.756l11.306 2.117c1.168.219 1.63 1.642.814 2.505l-7.902 8.359a1.5 1.5 0 0 0-.397 1.223l1.48 11.407c.153 1.177-1.058 2.057-2.131 1.548l-10.391-4.933a1.5 1.5 0 0 0-1.287 0l-10.39 4.933c-1.073.51-2.284-.37-2.131-1.548l1.48-11.407a1.5 1.5 0 0 0-.398-1.223L4.015 20.89c-.816-.863-.353-2.286.814-2.505l11.306-2.117a1.5 1.5 0 0 0 1.04-.756l5.508-10.098Z\\" fill=\\"currentColor\\" stroke=\\"none\\"></path>
                </svg></div>
            </div>
            <div class=\\"arco-rate-character arco-rate-character-full\\" role=\\"radio\\" aria-checked=\\"true\\" aria-setsize=\\"5\\" aria-posinset=\\"5\\">
              <div class=\\"arco-rate-character-left\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-star-fill\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
                  <path d=\\"M22.683 5.415c.568-1.043 2.065-1.043 2.634 0l5.507 10.098a1.5 1.5 0 0 0 1.04.756l11.306 2.117c1.168.219 1.63 1.642.814 2.505l-7.902 8.359a1.5 1.5 0 0 0-.397 1.223l1.48 11.407c.153 1.177-1.058 2.057-2.131 1.548l-10.391-4.933a1.5 1.5 0 0 0-1.287 0l-10.39 4.933c-1.073.51-2.284-.37-2.131-1.548l1.48-11.407a1.5 1.5 0 0 0-.398-1.223L4.015 20.89c-.816-.863-.353-2.286.814-2.505l11.306-2.117a1.5 1.5 0 0 0 1.04-.756l5.508-10.098Z\\" fill=\\"currentColor\\" stroke=\\"none\\"></path>
                </svg></div>
              <div class=\\"arco-rate-character-right\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-star-fill\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
                  <path d=\\"M22.683 5.415c.568-1.043 2.065-1.043 2.634 0l5.507 10.098a1.5 1.5 0 0 0 1.04.756l11.306 2.117c1.168.219 1.63 1.642.814 2.505l-7.902 8.359a1.5 1.5 0 0 0-.397 1.223l1.48 11.407c.153 1.177-1.058 2.057-2.131 1.548l-10.391-4.933a1.5 1.5 0 0 0-1.287 0l-10.39 4.933c-1.073.51-2.284-.37-2.131-1.548l1.48-11.407a1.5 1.5 0 0 0-.398-1.223L4.015 20.89c-.816-.863-.353-2.286.814-2.505l11.306-2.117a1.5 1.5 0 0 0 1.04-.756l5.508-10.098Z\\" fill=\\"currentColor\\" stroke=\\"none\\"></path>
                </svg></div>
            </div>
          </div>
        </div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Switch
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"switch\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><button type=\\"button\\" role=\\"switch\\" aria-checked=\\"false\\" class=\\"arco-switch arco-switch-type-circle\\"><span class=\\"arco-switch-handle\\"><span class=\\"arco-switch-handle-icon\\"></span></span><!--  prettier-ignore  -->
            <!--v-if-->
          </button></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Multiple Select
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"multiSelect\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-select-view arco-select-view-size-medium arco-select-view-has-suffix arco-select-view-has-placeholder arco-select-view-multiple arco-select\\"><span class=\\"arco-select-view-mirror\\">Please select ...</span>
          <!---->
          <transition-group-stub tag=\\"span\\" name=\\"input-tag-zoom\\" appear=\\"false\\" persisted=\\"false\\" css=\\"true\\" class=\\"arco-select-view-inner\\"><input class=\\"arco-select-view-input\\" style=\\"width: 12px;\\" placeholder=\\"Please select ...\\"></transition-group-stub>
          <!----><span class=\\"arco-select-view-suffix\\"><!----><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down arco-select-view-arrow-icon\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></span>
          <!---->
          <!----></span></span>
          <!---->
        </div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->Tree Select
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"treeSelect\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-select-view-single arco-select-view arco-select-view-size-medium\\" title=\\"\\"><!----><input class=\\"arco-select-view-input\\" readonly=\\"\\" placeholder=\\"Please select ...\\" value=\\"\\"><span class=\\"arco-select-view-value arco-select-view-value-hidden\\"><!----></span><span class=\\"arco-select-view-suffix\\"><!----><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down arco-select-view-arrow-icon\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></span>
          <!----></span></span>
          <!---->
        </div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\">
          <div class=\\"arco-space arco-space-horizontal arco-space-align-center\\">
            <!---->
            <div class=\\"arco-space-item\\" style=\\"margin-right: 8px;\\"><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"submit\\">
                <!--v-if-->Submit
              </button></div>
            <!---->
            <div class=\\"arco-space-item\\"><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
                <!--v-if-->Reset
              </button></div>
          </div>
        </div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
</form>
 {
 \\"size\\": \\"medium\\",
 \\"name\\": \\"\\",
 \\"section\\": \\"\\",
 \\"province\\": \\"haidian\\",
 \\"options\\": [],
 \\"date\\": \\"\\",
 \\"time\\": \\"\\",
 \\"radio\\": \\"radio one\\",
 \\"slider\\": 5,
 \\"score\\": 5,
 \\"switch\\": false,
 \\"multiSelect\\": [
 \\"section one\\"
 ],
 \\"treeSelect\\": \\"\\"
 }"
`;

exports[`<form> demo: render [validation2] correctly 1`] = `
"<form class=\\"arco-form arco-form-layout-horizontal arco-form-size-medium\\" style=\\"width: 600px;\\">
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>Username
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"name\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your username...\\" value=\\"\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>密码
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"password\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"password\\" placeholder=\\"please enter your password...\\" value=\\"\\"><!----><span class=\\"arco-input-suffix\\"><!----><span class=\\"arco-icon-hover\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-eye-invisible\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M14 14.5c-2.69 2-5.415 5.33-8 9.5 5.373 8.667 11.373 13 18 13 3.325 0 6.491-1.09 9.5-3.271M17.463 12.5C19 11 21.75 11 24 11c6.627 0 12.627 4.333 18 13-1.766 2.848-3.599 5.228-5.5 7.14\\"></path><path d=\\"M29 24a5 5 0 1 1-10 0 5 5 0 0 1 10 0ZM6.852 7.103l34.294 34.294\\"></path></svg></span>
          <!----></span></span>
        </div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>确认密码
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"password2\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"password\\" placeholder=\\"please confirm your password...\\" value=\\"\\"><!----><span class=\\"arco-input-suffix\\"><!----><span class=\\"arco-icon-hover\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-eye-invisible\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M14 14.5c-2.69 2-5.415 5.33-8 9.5 5.373 8.667 11.373 13 18 13 3.325 0 6.491-1.09 9.5-3.271M17.463 12.5C19 11 21.75 11 24 11c6.627 0 12.627 4.333 18 13-1.766 2.848-3.599 5.228-5.5 7.14\\"></path><path d=\\"M29 24a5 5 0 1 1-10 0 5 5 0 0 1 10 0ZM6.852 7.103l34.294 34.294\\"></path></svg></span>
          <!----></span></span>
        </div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>email
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"email\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your email...\\" value=\\"\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>IP
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"ip\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your ip...\\" value=\\"***********\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>URL
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"url\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your url...\\" value=\\"\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\"><strong class=\\"arco-form-item-label-required-symbol\\"><svg fill=\\"currentColor\\" viewBox=\\"0 0 1024 1024\\" width=\\"1em\\" height=\\"1em\\"><path d=\\"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\\"></path></svg></strong>match
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\" id=\\"match\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\"><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"please enter your match...\\" value=\\"\\"><!----><!----></span></div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
  <div class=\\"arco-row arco-row-align-start arco-row-justify-start arco-form-item arco-form-item-layout-horizontal\\">
    <div class=\\"arco-col arco-col-5 arco-form-item-label-col\\"><label class=\\"arco-form-item-label\\">
        <!--v-if-->
        <!--v-if-->
        <!--v-if-->
      </label></div>
    <div class=\\"arco-col arco-col-19 arco-form-item-wrapper-col\\">
      <div class=\\"arco-form-item-content-wrapper\\">
        <div class=\\"arco-form-item-content arco-form-item-content-flex\\">
          <div class=\\"arco-space arco-space-horizontal arco-space-align-center\\">
            <!---->
            <div class=\\"arco-space-item\\" style=\\"margin-right: 8px;\\"><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"submit\\">
                <!--v-if-->Submit
              </button></div>
            <!---->
            <div class=\\"arco-space-item\\"><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
                <!--v-if-->Reset
              </button></div>
          </div>
        </div>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
</form>
 {
 \\"name\\": \\"\\",
 \\"password\\": \\"\\",
 \\"password2\\": \\"\\",
 \\"email\\": \\"\\",
 \\"ip\\": \\"***********\\",
 \\"url\\": \\"\\",
 \\"match\\": \\"\\"
 }"
`;
