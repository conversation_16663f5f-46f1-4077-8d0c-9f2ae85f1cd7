// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<date-picker> demo: render [basic] correctly 1`] = `
"<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 200px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择日期\\" class=\\"arco-picker-start-time\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>"
`;

exports[`<date-picker> demo: render [control] correctly 1`] = `
"<div class=\\"arco-space arco-space-horizontal arco-space-align-center\\">
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-right: 8px;\\">
    <div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 200px;\\" visible=\\"false\\">
      <!--v-if-->
      <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择日期\\" class=\\"arco-picker-start-time\\" value=\\"2025-03-15\\"></div>
      <div class=\\"arco-picker-suffix\\"><span class=\\"arco-icon-hover arco-picker-icon-hover arco-picker-clear-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
        <!--v-if-->
      </div>
    </div>
    <!---->
  </div>
  <!---->
  <div class=\\"arco-space-item\\">
    <div class=\\"arco-picker arco-picker-range arco-picker-size-medium\\" style=\\"width: 300px;\\" visible=\\"false\\">
      <!--v-if-->
      <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"开始日期\\" value=\\"2025-03-15\\"></div><span class=\\"arco-picker-separator\\">-</span>
      <div class=\\"arco-picker-input\\"><input placeholder=\\"结束日期\\" value=\\"2025-03-15\\"></div>
      <div class=\\"arco-picker-suffix\\"><span class=\\"arco-icon-hover arco-picker-icon-hover arco-picker-clear-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
        <!--v-if-->
      </div>
    </div>
    <!---->
  </div>
</div>"
`;

exports[`<date-picker> demo: render [date-render] correctly 1`] = `
"<div class=\\"arco-picker arco-picker-size-medium\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择日期\\" class=\\"arco-picker-start-time\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>"
`;

exports[`<date-picker> demo: render [default-value] correctly 1`] = `
"<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 200px; margin-bottom: 24px; margin-right: 24px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择日期\\" class=\\"arco-picker-start-time\\" value=\\"2019-06-03\\"></div>
  <div class=\\"arco-picker-suffix\\"><span class=\\"arco-icon-hover arco-picker-icon-hover arco-picker-clear-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 240px; margin-bottom: 24px; margin-right: 24px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择日期\\" class=\\"arco-picker-start-time\\" readonly=\\"\\" value=\\"custom format: 2019-06-03\\"></div>
  <div class=\\"arco-picker-suffix\\"><span class=\\"arco-icon-hover arco-picker-icon-hover arco-picker-clear-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 200px; margin-bottom: 24px; margin-right: 24px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择日期\\" class=\\"arco-picker-start-time\\" value=\\"2019-06-03 08:00:00\\"></div>
  <div class=\\"arco-picker-suffix\\"><span class=\\"arco-icon-hover arco-picker-icon-hover arco-picker-clear-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 200px; margin-bottom: 24px; margin-right: 24px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择年份\\" class=\\"arco-picker-start-time\\" value=\\"2019\\"></div>
  <div class=\\"arco-picker-suffix\\"><span class=\\"arco-icon-hover arco-picker-icon-hover arco-picker-clear-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 200px; margin-bottom: 24px; margin-right: 24px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择月份\\" class=\\"arco-picker-start-time\\" value=\\"2019-06\\"></div>
  <div class=\\"arco-picker-suffix\\"><span class=\\"arco-icon-hover arco-picker-icon-hover arco-picker-clear-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 200px; margin-bottom: 24px; margin-right: 24px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择周\\" class=\\"arco-picker-start-time\\" value=\\"2019-31周\\"></div>
  <div class=\\"arco-picker-suffix\\"><span class=\\"arco-icon-hover arco-picker-icon-hover arco-picker-clear-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<div class=\\"arco-picker arco-picker-range arco-picker-size-medium\\" style=\\"width: 360px; margin-bottom: 24px; margin-right: 24px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"开始日期\\" value=\\"2019-08-08 00:00:00\\"></div><span class=\\"arco-picker-separator\\">-</span>
  <div class=\\"arco-picker-input\\"><input placeholder=\\"结束日期\\" value=\\"2019-08-18 00:00:00\\"></div>
  <div class=\\"arco-picker-suffix\\"><span class=\\"arco-icon-hover arco-picker-icon-hover arco-picker-clear-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<div class=\\"arco-picker arco-picker-range arco-picker-size-medium\\" style=\\"width: 300px; margin-bottom: 24px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"开始月份\\" value=\\"2019-08\\"></div><span class=\\"arco-picker-separator\\">-</span>
  <div class=\\"arco-picker-input\\"><input placeholder=\\"结束月份\\" value=\\"2020-06\\"></div>
  <div class=\\"arco-picker-suffix\\"><span class=\\"arco-icon-hover arco-picker-icon-hover arco-picker-clear-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>"
`;

exports[`<date-picker> demo: render [disabled] correctly 1`] = `
"<div class=\\"arco-picker arco-picker-size-medium arco-picker-disabled\\" style=\\"width: 200px; margin-bottom: 20px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input disabled=\\"\\" placeholder=\\"请选择日期\\" class=\\"arco-picker-start-time\\" value=\\"2020-08-08\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<br>
<div class=\\"arco-picker arco-picker-range arco-picker-size-medium arco-picker-disabled\\" style=\\"width: 300px; margin-bottom: 20px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input disabled=\\"\\" placeholder=\\"开始日期\\" value=\\"2020-08-08\\"></div><span class=\\"arco-picker-separator\\">-</span>
  <div class=\\"arco-picker-input arco-picker-input-active\\"><input disabled=\\"\\" placeholder=\\"结束日期\\" value=\\"2020-08-18\\"></div>
  <div class=\\"arco-picker-suffix\\"><span class=\\"arco-icon-hover arco-picker-icon-hover arco-picker-clear-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<br>
<div class=\\"arco-picker arco-picker-range arco-picker-size-medium\\" style=\\"width: 300px; margin-bottom: 20px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input disabled=\\"\\" placeholder=\\"开始日期\\" value=\\"2020-08-08\\"></div><span class=\\"arco-picker-separator\\">-</span>
  <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"结束日期\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<br>
<div class=\\"arco-picker arco-picker-range arco-picker-size-medium\\" style=\\"width: 380px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input disabled=\\"\\" placeholder=\\"开始日期\\" value=\\"2020-08-08 02:02:02\\"></div><span class=\\"arco-picker-separator\\">-</span>
  <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"结束日期\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>"
`;

exports[`<date-picker> demo: render [disabled-date] correctly 1`] = `
"<div>
  <div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 200px; margin-right: 24px; margin-bottom: 24px;\\" visible=\\"false\\">
    <!--v-if-->
    <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择日期\\" class=\\"arco-picker-start-time\\"></div>
    <div class=\\"arco-picker-suffix\\">
      <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
      <!--v-if-->
    </div>
  </div>
  <!---->
  <div class=\\"arco-picker arco-picker-range arco-picker-size-medium\\" style=\\"width: 360px; margin-right: 24px; margin-bottom: 24px;\\" visible=\\"false\\">
    <!--v-if-->
    <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"开始日期\\"></div><span class=\\"arco-picker-separator\\">-</span>
    <div class=\\"arco-picker-input\\"><input placeholder=\\"结束日期\\"></div>
    <div class=\\"arco-picker-suffix\\">
      <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
      <!--v-if-->
    </div>
  </div>
  <!---->
  <div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 200px; margin-right: 24px; margin-bottom: 24px;\\" visible=\\"false\\">
    <!--v-if-->
    <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择日期\\" class=\\"arco-picker-start-time\\"></div>
    <div class=\\"arco-picker-suffix\\">
      <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
      <!--v-if-->
    </div>
  </div>
  <!---->
  <div class=\\"arco-picker arco-picker-range arco-picker-size-medium\\" style=\\"width: 360px; margin-bottom: 24px;\\" visible=\\"false\\">
    <!--v-if-->
    <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"开始日期\\"></div><span class=\\"arco-picker-separator\\">-</span>
    <div class=\\"arco-picker-input\\"><input placeholder=\\"结束日期\\"></div>
    <div class=\\"arco-picker-suffix\\">
      <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
      <!--v-if-->
    </div>
  </div>
  <!---->
</div>"
`;

exports[`<date-picker> demo: render [disabled-date-advance] correctly 1`] = `
"<div class=\\"arco-picker arco-picker-range arco-picker-size-medium\\" style=\\"width: 300px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"开始日期\\"></div><span class=\\"arco-picker-separator\\">-</span>
  <div class=\\"arco-picker-input\\"><input placeholder=\\"结束日期\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>"
`;

exports[`<date-picker> demo: render [extra] correctly 1`] = `
"<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 200px; margin-bottom: 20px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择日期\\" class=\\"arco-picker-start-time\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<br>
<div class=\\"arco-picker arco-picker-range arco-picker-size-medium\\" style=\\"width: 360px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"开始日期\\"></div><span class=\\"arco-picker-separator\\">-</span>
  <div class=\\"arco-picker-input\\"><input placeholder=\\"结束日期\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>"
`;

exports[`<date-picker> demo: render [month] correctly 1`] = `
"<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 200px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择月份\\" class=\\"arco-picker-start-time\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>"
`;

exports[`<date-picker> demo: render [panel-only] correctly 1`] = `
"<div>
  <div class=\\"arco-picker-container arco-picker-container-panel-only\\" style=\\"width: 268px;\\" visible=\\"false\\">
    <!--v-if-->
    <div class=\\"arco-picker-panel-wrapper\\">
      <div class=\\"arco-panel-date\\">
        <div class=\\"arco-panel-date-inner\\">
          <div class=\\"arco-picker-header\\">
            <div class=\\"arco-picker-header-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-double-left\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
                <path d=\\"M36.857 9.9 22.715 24.042l14.142 14.142M25.544 9.9 11.402 24.042l14.142 14.142\\"></path>
              </svg></div>
            <div class=\\"arco-picker-header-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-left\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
                <path d=\\"M32 8.4 16.444 23.956 32 39.513\\"></path>
              </svg></div>
            <div class=\\"arco-picker-header-title\\"><span class=\\"arco-picker-header-label\\">2019</span><span>-</span><span class=\\"arco-picker-header-label\\">06</span></div>
            <div class=\\"arco-picker-header-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-right\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
                <path d=\\"m16 39.513 15.556-15.557L16 8.4\\"></path>
              </svg></div>
            <div class=\\"arco-picker-header-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-double-right\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
                <path d=\\"m11.143 38.1 14.142-14.142L11.143 9.816M22.456 38.1l14.142-14.142L22.456 9.816\\"></path>
              </svg></div>
          </div>
          <div class=\\"arco-picker-week-list\\">
            <div class=\\"arco-picker-week-list-item\\">日</div>
            <div class=\\"arco-picker-week-list-item\\">一</div>
            <div class=\\"arco-picker-week-list-item\\">二</div>
            <div class=\\"arco-picker-week-list-item\\">三</div>
            <div class=\\"arco-picker-week-list-item\\">四</div>
            <div class=\\"arco-picker-week-list-item\\">五</div>
            <div class=\\"arco-picker-week-list-item\\">六</div>
          </div>
          <div class=\\"arco-picker-body\\">
            <div class=\\"arco-picker-row\\">
              <!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">26</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">27</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">28</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">29</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">30</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">31</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">1</div>
                </div>
              </div>
            </div>
            <div class=\\"arco-picker-row\\">
              <!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">2</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-selected\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">3</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">4</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">5</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">6</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">7</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">8</div>
                </div>
              </div>
            </div>
            <div class=\\"arco-picker-row\\">
              <!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">9</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">10</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">11</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">12</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">13</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">14</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">15</div>
                </div>
              </div>
            </div>
            <div class=\\"arco-picker-row\\">
              <!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">16</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">17</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">18</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">19</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">20</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">21</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">22</div>
                </div>
              </div>
            </div>
            <div class=\\"arco-picker-row\\">
              <!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">23</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">24</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">25</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">26</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">27</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">28</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">29</div>
                </div>
              </div>
            </div>
            <div class=\\"arco-picker-row\\">
              <!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">30</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">1</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">2</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">3</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">4</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">5</div>
                </div>
              </div><!-- 一年中的第几周，只在 week 模式下显示 -->
              <div class=\\"arco-picker-cell\\">
                <div class=\\"arco-picker-date\\">
                  <div class=\\"arco-picker-date-value\\">6</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!--v-if-->
        <!--v-if-->
      </div>
      <div class=\\"arco-picker-footer\\">
        <!--v-if-->
        <div class=\\"arco-picker-footer-now-wrapper\\"><a class=\\"arco-link arco-link-status-normal\\">
            <!--v-if-->今天
          </a></div>
        <!--v-if-->
      </div>
    </div>
    <!--v-if-->
  </div>
  <div class=\\"arco-picker-range-container arco-picker-range-container-panel-only\\" style=\\"width: 560px; margin-top: 20px;\\">
    <!--v-if-->
    <div class=\\"arco-picker-range-panel-wrapper\\">
      <!-- panel -->
      <div class=\\"arco-picker-range\\">
        <div class=\\"arco-picker-range-wrapper\\">
          <!-- week -->
          <!-- date -->
          <div class=\\"arco-panel-date\\">
            <div class=\\"arco-panel-date-inner\\">
              <div class=\\"arco-picker-header\\">
                <div class=\\"arco-picker-header-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-double-left\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
                    <path d=\\"M36.857 9.9 22.715 24.042l14.142 14.142M25.544 9.9 11.402 24.042l14.142 14.142\\"></path>
                  </svg></div>
                <div class=\\"arco-picker-header-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-left\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
                    <path d=\\"M32 8.4 16.444 23.956 32 39.513\\"></path>
                  </svg></div>
                <div class=\\"arco-picker-header-title\\"><span class=\\"arco-picker-header-label\\">2019</span><span>-</span><span class=\\"arco-picker-header-label\\">08</span></div>
                <div class=\\"arco-picker-header-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-right\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
                    <path d=\\"m16 39.513 15.556-15.557L16 8.4\\"></path>
                  </svg></div>
                <div class=\\"arco-picker-header-icon arco-picker-header-icon-hidden\\">
                  <!--v-if-->
                </div>
              </div>
              <div class=\\"arco-picker-week-list\\">
                <div class=\\"arco-picker-week-list-item\\">日</div>
                <div class=\\"arco-picker-week-list-item\\">一</div>
                <div class=\\"arco-picker-week-list-item\\">二</div>
                <div class=\\"arco-picker-week-list-item\\">三</div>
                <div class=\\"arco-picker-week-list-item\\">四</div>
                <div class=\\"arco-picker-week-list-item\\">五</div>
                <div class=\\"arco-picker-week-list-item\\">六</div>
              </div>
              <div class=\\"arco-picker-body\\">
                <div class=\\"arco-picker-row\\">
                  <!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">28</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">29</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">30</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">31</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-range-start arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">1</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">2</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">3</div>
                    </div>
                  </div>
                </div>
                <div class=\\"arco-picker-row\\">
                  <!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">4</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">5</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">6</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">7</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">8</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">9</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">10</div>
                    </div>
                  </div>
                </div>
                <div class=\\"arco-picker-row\\">
                  <!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">11</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">12</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">13</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">14</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">15</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">16</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">17</div>
                    </div>
                  </div>
                </div>
                <div class=\\"arco-picker-row\\">
                  <!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">18</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">19</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">20</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">21</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">22</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">23</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">24</div>
                    </div>
                  </div>
                </div>
                <div class=\\"arco-picker-row\\">
                  <!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">25</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">26</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">27</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">28</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">29</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">30</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">31</div>
                    </div>
                  </div>
                </div>
                <div class=\\"arco-picker-row\\">
                  <!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">1</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">2</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">3</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">4</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">5</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">6</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">7</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!--v-if-->
            <!--v-if-->
          </div>
          <div class=\\"arco-panel-date\\">
            <div class=\\"arco-panel-date-inner\\">
              <div class=\\"arco-picker-header\\">
                <div class=\\"arco-picker-header-icon arco-picker-header-icon-hidden\\">
                  <!--v-if-->
                </div>
                <div class=\\"arco-picker-header-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-left\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
                    <path d=\\"M32 8.4 16.444 23.956 32 39.513\\"></path>
                  </svg></div>
                <div class=\\"arco-picker-header-title\\"><span class=\\"arco-picker-header-label\\">2020</span><span>-</span><span class=\\"arco-picker-header-label\\">06</span></div>
                <div class=\\"arco-picker-header-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-right\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
                    <path d=\\"m16 39.513 15.556-15.557L16 8.4\\"></path>
                  </svg></div>
                <div class=\\"arco-picker-header-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-double-right\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
                    <path d=\\"m11.143 38.1 14.142-14.142L11.143 9.816M22.456 38.1l14.142-14.142L22.456 9.816\\"></path>
                  </svg></div>
              </div>
              <div class=\\"arco-picker-week-list\\">
                <div class=\\"arco-picker-week-list-item\\">日</div>
                <div class=\\"arco-picker-week-list-item\\">一</div>
                <div class=\\"arco-picker-week-list-item\\">二</div>
                <div class=\\"arco-picker-week-list-item\\">三</div>
                <div class=\\"arco-picker-week-list-item\\">四</div>
                <div class=\\"arco-picker-week-list-item\\">五</div>
                <div class=\\"arco-picker-week-list-item\\">六</div>
              </div>
              <div class=\\"arco-picker-body\\">
                <div class=\\"arco-picker-row\\">
                  <!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">31</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view arco-picker-cell-range-end arco-picker-cell-in-range\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">1</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">2</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">3</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">4</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">5</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">6</div>
                    </div>
                  </div>
                </div>
                <div class=\\"arco-picker-row\\">
                  <!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">7</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">8</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">9</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">10</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">11</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">12</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">13</div>
                    </div>
                  </div>
                </div>
                <div class=\\"arco-picker-row\\">
                  <!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">14</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">15</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">16</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">17</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">18</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">19</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">20</div>
                    </div>
                  </div>
                </div>
                <div class=\\"arco-picker-row\\">
                  <!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">21</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">22</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">23</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">24</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">25</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">26</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">27</div>
                    </div>
                  </div>
                </div>
                <div class=\\"arco-picker-row\\">
                  <!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">28</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">29</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell arco-picker-cell-in-view\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">30</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">1</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">2</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">3</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">4</div>
                    </div>
                  </div>
                </div>
                <div class=\\"arco-picker-row\\">
                  <!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">5</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">6</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">7</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">8</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">9</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">10</div>
                    </div>
                  </div><!-- 一年中的第几周，只在 week 模式下显示 -->
                  <div class=\\"arco-picker-cell\\">
                    <div class=\\"arco-picker-date\\">
                      <div class=\\"arco-picker-date-value\\">11</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!--v-if-->
            <!--v-if-->
          </div>
        </div>
      </div><!-- footer -->
      <div class=\\"arco-picker-footer\\">
        <!--v-if-->
        <!--v-if-->
        <!--v-if-->
      </div>
    </div>
    <!--v-if-->
  </div>
</div>"
`;

exports[`<date-picker> demo: render [prefix] correctly 1`] = `
"<div>
  <div>
    <div class=\\"arco-picker arco-picker-size-medium arco-picker-has-prefix\\" style=\\"width: 300px;\\" visible=\\"false\\">
      <div class=\\"arco-picker-prefix\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-info-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
          <path d=\\"M24 20v14m0-16v-4m18 10c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path>
        </svg></div>
      <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择日期\\" class=\\"arco-picker-start-time\\"></div>
      <div class=\\"arco-picker-suffix\\">
        <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
        <!--v-if-->
      </div>
    </div>
    <!---->
  </div>
  <div class=\\"arco-picker arco-picker-range arco-picker-size-medium arco-picker-has-prefix\\" style=\\"width: 400px; margin-top: 20px;\\" visible=\\"false\\">
    <div class=\\"arco-picker-prefix\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-info-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
        <path d=\\"M24 20v14m0-16v-4m18 10c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path>
      </svg></div>
    <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"开始日期\\" value=\\"2019-08-08 00:00:00\\"></div><span class=\\"arco-picker-separator\\">-</span>
    <div class=\\"arco-picker-input\\"><input placeholder=\\"结束日期\\" value=\\"2019-08-18 00:00:00\\"></div>
    <div class=\\"arco-picker-suffix\\"><span class=\\"arco-icon-hover arco-picker-icon-hover arco-picker-clear-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
      <!--v-if-->
    </div>
  </div>
  <!---->
</div>"
`;

exports[`<date-picker> demo: render [quarter] correctly 1`] = `
"<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 200px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择季度\\" class=\\"arco-picker-start-time\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>"
`;

exports[`<date-picker> demo: render [range] correctly 1`] = `
"<div class=\\"arco-picker arco-picker-range arco-picker-size-medium\\" style=\\"width: 254px; margin-bottom: 20px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"开始日期\\"></div><span class=\\"arco-picker-separator\\">-</span>
  <div class=\\"arco-picker-input\\"><input placeholder=\\"结束日期\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<br>
<div class=\\"arco-picker arco-picker-range arco-picker-size-medium\\" style=\\"width: 254px; margin-bottom: 20px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"开始周\\"></div><span class=\\"arco-picker-separator\\">-</span>
  <div class=\\"arco-picker-input\\"><input placeholder=\\"结束周\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<br>
<div class=\\"arco-picker arco-picker-range arco-picker-size-medium\\" style=\\"width: 254px; margin-bottom: 20px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"开始月份\\"></div><span class=\\"arco-picker-separator\\">-</span>
  <div class=\\"arco-picker-input\\"><input placeholder=\\"结束月份\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<br>
<div class=\\"arco-picker arco-picker-range arco-picker-size-medium\\" style=\\"width: 254px; margin-bottom: 20px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"开始年份\\"></div><span class=\\"arco-picker-separator\\">-</span>
  <div class=\\"arco-picker-input\\"><input placeholder=\\"结束年份\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<br>
<div class=\\"arco-picker arco-picker-range arco-picker-size-medium\\" style=\\"width: 254px; margin-bottom: 20px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"开始季度\\"></div><span class=\\"arco-picker-separator\\">-</span>
  <div class=\\"arco-picker-input\\"><input placeholder=\\"结束季度\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<br>
<div class=\\"arco-picker arco-picker-range arco-picker-size-medium\\" style=\\"width: 380px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"开始日期\\"></div><span class=\\"arco-picker-separator\\">-</span>
  <div class=\\"arco-picker-input\\"><input placeholder=\\"结束日期\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>"
`;

exports[`<date-picker> demo: render [shortcuts] correctly 1`] = `
"<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 300px; margin-bottom: 24px; margin-right: 24px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择日期\\" class=\\"arco-picker-start-time\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 300px; margin-bottom: 24px; margin-right: 24px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择月份\\" class=\\"arco-picker-start-time\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<div class=\\"arco-picker arco-picker-range arco-picker-size-medium\\" style=\\"width: 400px; margin-bottom: 24px; margin-right: 24px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"开始日期\\"></div><span class=\\"arco-picker-separator\\">-</span>
  <div class=\\"arco-picker-input\\"><input placeholder=\\"结束日期\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<div class=\\"arco-picker arco-picker-range arco-picker-size-medium\\" style=\\"width: 300px; margin-bottom: 24px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"开始月份\\"></div><span class=\\"arco-picker-separator\\">-</span>
  <div class=\\"arco-picker-input\\"><input placeholder=\\"结束月份\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>"
`;

exports[`<date-picker> demo: render [shortcuts-position] correctly 1`] = `
"<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 254px; margin-bottom: 20px; margin-right: 24px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择日期\\" class=\\"arco-picker-start-time\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<div class=\\"arco-picker arco-picker-range arco-picker-size-medium\\" style=\\"width: 300px; margin-bottom: 20px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"开始日期\\"></div><span class=\\"arco-picker-separator\\">-</span>
  <div class=\\"arco-picker-input\\"><input placeholder=\\"结束日期\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<br>
<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 254px; margin-right: 24px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择日期\\" class=\\"arco-picker-start-time\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<div class=\\"arco-picker arco-picker-range arco-picker-size-medium\\" style=\\"width: 300px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"开始日期\\"></div><span class=\\"arco-picker-separator\\">-</span>
  <div class=\\"arco-picker-input\\"><input placeholder=\\"结束日期\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>"
`;

exports[`<date-picker> demo: render [showtime] correctly 1`] = `
"<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 220px; margin: 0px 24px 24px 0px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择日期\\" class=\\"arco-picker-start-time\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 220px; margin: 0px 24px 24px 0px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择日期\\" class=\\"arco-picker-start-time\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<div class=\\"arco-picker arco-picker-range arco-picker-size-medium\\" style=\\"width: 360px; margin: 0px 24px 24px 0px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"开始日期\\"></div><span class=\\"arco-picker-separator\\">-</span>
  <div class=\\"arco-picker-input\\"><input placeholder=\\"结束日期\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>"
`;

exports[`<date-picker> demo: render [size] correctly 1`] = `
"<div style=\\"margin-bottom: 20px;\\"><span class=\\"arco-radio-group-button arco-radio-group-size-medium arco-radio-group-direction-horizontal\\"><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"mini\\"><span class=\\"arco-radio-button-content\\">mini</span></label><label class=\\"arco-radio-button arco-radio-checked\\"><input type=\\"radio\\" checked=\\"\\" class=\\"arco-radio-target\\" value=\\"small\\"><span class=\\"arco-radio-button-content\\">small</span></label><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"medium\\"><span class=\\"arco-radio-button-content\\">medium</span></label><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"large\\"><span class=\\"arco-radio-button-content\\">large</span></label></span></div>
<div class=\\"arco-picker arco-picker-size-small\\" style=\\"width: 254px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择日期\\" class=\\"arco-picker-start-time\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>"
`;

exports[`<date-picker> demo: render [trigger-element] correctly 1`] = `
"<div class=\\"arco-space arco-space-horizontal arco-space-align-center\\">
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-right: 8px;\\"><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
      <!--v-if-->请选择日期
    </button>
    <!---->
  </div>
  <!---->
  <div class=\\"arco-space-item\\"><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
      <!--v-if-->请选择日期范围
    </button>
    <!---->
  </div>
</div>"
`;

exports[`<date-picker> demo: render [week] correctly 1`] = `
"<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 200px; margin: 0px 24px 24px 0px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择周\\" class=\\"arco-picker-start-time\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>
<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 200px; margin: 0px 24px 24px 0px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择周\\" class=\\"arco-picker-start-time\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>"
`;

exports[`<date-picker> demo: render [year] correctly 1`] = `
"<div class=\\"arco-picker arco-picker-size-medium\\" style=\\"width: 200px;\\" visible=\\"false\\">
  <!--v-if-->
  <div class=\\"arco-picker-input\\"><input placeholder=\\"请选择年份\\" class=\\"arco-picker-start-time\\"></div>
  <div class=\\"arco-picker-suffix\\">
    <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
    <!--v-if-->
  </div>
</div>"
`;
