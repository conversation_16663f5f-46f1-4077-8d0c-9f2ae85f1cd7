```yaml
changelog: true
```

## 2.56.3

`2024-10-25`

### 🐛 BugFix

- fix readonly not working in button mode ([#3314](https://github.com/arco-design/arco-design-vue/pull/3314))


## 2.56.0

`2024-07-26`

### 🆕 Feature

- Added 'keydown' event to disable default behavior ([#3248](https://github.com/arco-design/arco-design-vue/pull/3248))


## 2.54.6

`2024-03-01`

### 🐛 BugFix

- fix overlap between step button and suffix/append ([#3005](https://github.com/arco-design/arco-design-vue/pull/3005))


## 2.54.5

`2024-02-21`

### 🐛 BugFix

- fix v-model not working ([#2961](https://github.com/arco-design/arco-design-vue/pull/2961))


## 2.54.4

`2024-02-02`

### 🐛 BugFix

- Fix change event triggering mechanism ([#2915](https://github.com/arco-design/arco-design-vue/pull/2915))


## 2.52.0

`2023-09-22`

### 🆕 Feature

- Add `input-attrs` attribute ([#2716](https://github.com/arco-design/arco-design-vue/pull/2716))


## 2.50.2

`2023-08-25`

### 💎 Enhancement

- Optimize the long-press effect of the step button ([#2668](https://github.com/arco-design/arco-design-vue/pull/2668))


## 2.49.0

`2023-07-21`

### 🆕 Feature

- add slots for input-number box value manipulation icons ([#2560](https://github.com/arco-design/arco-design-vue/pull/2560))


## 2.47.0

`2023-06-02`

### ⚠️ Important Attention

- `hide-button` also takes effect when `mode="button"` ([#2461](https://github.com/arco-design/arco-design-vue/pull/2461))


## 2.43.2

`2023-02-24`

### 🐛 BugFix

- fix display step button in disabled state ([#2169](https://github.com/arco-design/arco-design-vue/pull/2169))

## 2.42.1

`2023-02-03`

### 🐛 BugFix

- fix step-button is not properly disabled or enabled when min or max is changed ([#1777](https://github.com/arco-design/arco-design-vue/pull/1777))


## 2.34.0

`2022-07-29`

### 💎 Enhancement

- Support `read-only` prop ([#1408](https://github.com/arco-design/arco-design-vue/pull/1408))


## 2.32.1

`2022-07-01`

### 🐛 BugFix

- Fix the problem that '0' will be omitted after setting precision ([#1368](https://github.com/arco-design/arco-design-vue/pull/1368))


## 2.32.0

`2022-06-24`

### 🐛 BugFix

- fix clear error outside of form ([#1329](https://github.com/arco-design/arco-design-vue/pull/1329))


## 2.29.1

`2022-06-02`

### 💎 Enhancement

- Add keyboard event, show step button when focused ([#1224](https://github.com/arco-design/arco-design-vue/pull/1224))


## 2.29.0

`2022-05-27`

### 🐛 BugFix

- Fixed the issue that clear did not trigger form validation ([#1204](https://github.com/arco-design/arco-design-vue/pull/1204))


## 2.27.0

`2022-05-13`

### 🆕 Feature

- Added modelEvent property and input event ([#1115](https://github.com/arco-design/arco-design-vue/pull/1115))


## 2.25.1

`2022-04-27`

### 💅 Style

- Fix the display position of the clear button ([#1048](https://github.com/arco-design/arco-design-vue/pull/1048))


## 2.25.0

`2022-04-22`

### 🐛 BugFix

- Fix the problem that the error attribute is invalid ([#1026](https://github.com/arco-design/arco-design-vue/pull/1026))


## 2.22.0

`2022-04-01`

### 🐛 BugFix

- Fixed display error when switching `mode` ([#909](https://github.com/arco-design/arco-design-vue/pull/909))


## 2.14.3

`2022-01-12`

### 🐛 BugFix

- Fix the problem that the cursor position changes when inputting ([#552](https://github.com/arco-design/arco-design-vue/pull/552))


## 2.12.0

`2021-12-24`

### ⚠️ Important Attention

- Modify the change event to be triggered only when out of focus and press Enter to solve the problem that the control cannot be input ([#452](https://github.com/arco-design/arco-design-vue/pull/452))

### 🆕 Feature

- Add support for input slot ([#451](https://github.com/arco-design/arco-design-vue/pull/451))


## 2.11.0

`2021-12-17`

### 🐛 BugFix

- Fix the problem of displaying errors when `model-value` is 0 ([#407](https://github.com/arco-design/arco-design-vue/pull/407))


## 2.10.0

`2021-12-10`

### 🐛 BugFix

- Fix the problem of repeatable input of `-` and `.` ([#359](https://github.com/arco-design/arco-design-vue/pull/359))


## 2.8.0

`2021-12-01`

### 🐛 BugFix

- Fix the problem that the button does not add size in button mode ([#293](https://github.com/arco-design/arco-design-vue/pull/293))


## 2.1.1

`2021-11-08`

### 🐛 BugFix

- Fix the problem of inputting the negative sign error ([#114](https://github.com/arco-design/arco-design-vue/pull/114))


## 2.1.0

`2021-11-05`

### 🐛 BugFix

- Fix the problem of invalid accuracy ([#93](https://github.com/arco-design/arco-design-vue/pull/93))

