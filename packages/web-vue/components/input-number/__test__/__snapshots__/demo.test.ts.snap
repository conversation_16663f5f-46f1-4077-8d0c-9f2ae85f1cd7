// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<input-number> demo: render [basic] correctly 1`] = `
"<div class=\\"arco-space arco-space-vertical\\">
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 24px;\\"><span class=\\"arco-input-wrapper arco-input-number arco-input-number-mode-embed arco-input-number-size-medium input-demo\\" style=\\"width: 320px;\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"Please Enter\\" role=\\"spinbutton\\" aria-valuemax=\\"100\\" aria-valuemin=\\"10\\" aria-valuenow=\\"15\\" value=\\"15\\"><!----><span class=\\"arco-input-suffix\\"><!----><!----><div class=\\"arco-input-number-step\\"><button class=\\"arco-input-number-step-button\\" type=\\"button\\" tabindex=\\"-1\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-up\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 30.557 24.043 15 8.487 30.557\\"></path></svg></button><button class=\\"arco-input-number-step-button\\" type=\\"button\\" tabindex=\\"-1\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></button></div><!----></span></span></div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 24px;\\"><span class=\\"arco-input-wrapper arco-input-number arco-input-number-mode-embed arco-input-number-size-medium input-demo\\" style=\\"width: 320px;\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"Please Enter\\" role=\\"spinbutton\\" aria-valuemax=\\"100\\" aria-valuemin=\\"10\\" aria-valuenow=\\"\\" value=\\"\\"><!----><span class=\\"arco-input-suffix\\"><!----><!----><div class=\\"arco-input-number-step\\"><button class=\\"arco-input-number-step-button\\" type=\\"button\\" tabindex=\\"-1\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-up\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 30.557 24.043 15 8.487 30.557\\"></path></svg></button><button class=\\"arco-input-number-step-button\\" type=\\"button\\" tabindex=\\"-1\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></button></div><!----></span></span></div>
  <!---->
  <div class=\\"arco-space-item\\"><span class=\\"arco-input-wrapper arco-input-disabled arco-input-number arco-input-number-mode-embed arco-input-number-size-medium input-demo\\" style=\\"width: 320px;\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"Please Enter\\" disabled=\\"\\" role=\\"spinbutton\\" aria-valuemax=\\"Infinity\\" aria-valuemin=\\"-Infinity\\" aria-valuenow=\\"500\\" value=\\"500\\"><!----><span class=\\"arco-input-suffix\\"><!----><!----><div class=\\"arco-input-number-step\\"><button class=\\"arco-input-number-step-button arco-input-number-step-button-disabled\\" type=\\"button\\" tabindex=\\"-1\\" disabled=\\"\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-up\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 30.557 24.043 15 8.487 30.557\\"></path></svg></button><button class=\\"arco-input-number-step-button arco-input-number-step-button-disabled\\" type=\\"button\\" tabindex=\\"-1\\" disabled=\\"\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></button></div><!----></span></span></div>
</div>"
`;

exports[`<input-number> demo: render [format] correctly 1`] = `"<span class=\\"arco-input-wrapper arco-input-number arco-input-number-mode-embed arco-input-number-size-medium input-demo\\" style=\\"width: 320px;\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"Please Enter\\" role=\\"spinbutton\\" aria-valuemax=\\"Infinity\\" aria-valuemin=\\"0\\" aria-valuenow=\\"12,000\\" value=\\"12,000\\"><!----><span class=\\"arco-input-suffix\\"><!----><!----><div class=\\"arco-input-number-step\\"><button class=\\"arco-input-number-step-button\\" type=\\"button\\" tabindex=\\"-1\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-up\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 30.557 24.043 15 8.487 30.557\\"></path></svg></button><button class=\\"arco-input-number-step-button\\" type=\\"button\\" tabindex=\\"-1\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></button></div><!----></span></span>"`;

exports[`<input-number> demo: render [mode] correctly 1`] = `"<span class=\\"arco-input-outer arco-input-outer-size-medium arco-input-number arco-input-number-mode-button arco-input-number-size-medium input-demo\\" style=\\"width: 320px;\\"><span class=\\"arco-input-prepend\\"><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal arco-btn-only-icon arco-input-number-step-button\\" type=\\"button\\" tabindex=\\"-1\\"><span class=\\"arco-btn-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-minus\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M5 24h38\\"></path></svg></span></button></span><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"Please Enter\\" role=\\"spinbutton\\" aria-valuemax=\\"Infinity\\" aria-valuemin=\\"-Infinity\\" aria-valuenow=\\"500\\" value=\\"500\\"><!----><!----></span><span class=\\"arco-input-append\\"><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal arco-btn-only-icon arco-input-number-step-button\\" type=\\"button\\" tabindex=\\"-1\\"><span class=\\"arco-btn-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-plus\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M5 24h38M24 5v38\\"></path></svg></span></button></span></span>"`;

exports[`<input-number> demo: render [model] correctly 1`] = `
"<span class=\\"arco-input-wrapper arco-input-number arco-input-number-mode-embed arco-input-number-size-medium input-demo\\" style=\\"width: 320px;\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"Please Enter\\" role=\\"spinbutton\\" aria-valuemax=\\"100\\" aria-valuemin=\\"10\\" aria-valuenow=\\"15\\" value=\\"15\\"><!----><span class=\\"arco-input-suffix\\"><!----><!----><div class=\\"arco-input-number-step\\"><button class=\\"arco-input-number-step-button\\" type=\\"button\\" tabindex=\\"-1\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-up\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 30.557 24.043 15 8.487 30.557\\"></path></svg></button><button class=\\"arco-input-number-step-button\\" type=\\"button\\" tabindex=\\"-1\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></button></div><!----></span></span>
<div>value: 15</div>"
`;

exports[`<input-number> demo: render [precision] correctly 1`] = `
"<div class=\\"arco-space arco-space-vertical\\">
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 24px;\\"><span class=\\"arco-input-wrapper arco-input-number arco-input-number-mode-embed arco-input-number-size-medium input-demo\\" style=\\"width: 320px;\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"Please Enter\\" role=\\"spinbutton\\" aria-valuemax=\\"Infinity\\" aria-valuemin=\\"-Infinity\\" aria-valuenow=\\"3.60\\" value=\\"3.60\\"><!----><span class=\\"arco-input-suffix\\"><!----><!----><div class=\\"arco-input-number-step\\"><button class=\\"arco-input-number-step-button\\" type=\\"button\\" tabindex=\\"-1\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-up\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 30.557 24.043 15 8.487 30.557\\"></path></svg></button><button class=\\"arco-input-number-step-button\\" type=\\"button\\" tabindex=\\"-1\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></button></div><!----></span></span></div>
  <!---->
  <div class=\\"arco-space-item\\"><span class=\\"arco-input-wrapper arco-input-number arco-input-number-mode-embed arco-input-number-size-medium input-demo\\" style=\\"width: 320px;\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"Please Enter\\" role=\\"spinbutton\\" aria-valuemax=\\"Infinity\\" aria-valuemin=\\"-Infinity\\" aria-valuenow=\\"1.22\\" value=\\"1.22\\"><!----><span class=\\"arco-input-suffix\\"><!----><!----><div class=\\"arco-input-number-step\\"><button class=\\"arco-input-number-step-button\\" type=\\"button\\" tabindex=\\"-1\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-up\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 30.557 24.043 15 8.487 30.557\\"></path></svg></button><button class=\\"arco-input-number-step-button\\" type=\\"button\\" tabindex=\\"-1\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></button></div><!----></span></span></div>
</div>"
`;

exports[`<input-number> demo: render [prefix] correctly 1`] = `
"<div class=\\"arco-space arco-space-vertical\\">
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 24px;\\"><span class=\\"arco-input-wrapper arco-input-number arco-input-number-mode-embed arco-input-number-size-medium\\" style=\\"width: 320px;\\"><span class=\\"arco-input-prefix\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-user\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 37c0-4.97 4.03-8 9-8h16c4.97 0 9 3.03 9 8v3a1 1 0 0 1-1 1H8a1 1 0 0 1-1-1v-3Z\\"></path><circle cx=\\"24\\" cy=\\"15\\" r=\\"8\\"></circle></svg></span><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"Please enter something\\" role=\\"spinbutton\\" aria-valuemax=\\"Infinity\\" aria-valuemin=\\"-Infinity\\" aria-valuenow=\\"\\" value=\\"\\">
    <!----><span class=\\"arco-input-suffix\\"><!----><!----><div class=\\"arco-input-number-step\\"><button class=\\"arco-input-number-step-button\\" type=\\"button\\" tabindex=\\"-1\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-up\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 30.557 24.043 15 8.487 30.557\\"></path></svg></button><button class=\\"arco-input-number-step-button\\" type=\\"button\\" tabindex=\\"-1\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></button></div><!----></span></span>
  </div>
  <!---->
  <div class=\\"arco-space-item\\"><span class=\\"arco-input-wrapper arco-input-number arco-input-number-mode-embed arco-input-number-size-medium\\" style=\\"width: 320px;\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"Please enter something\\" role=\\"spinbutton\\" aria-valuemax=\\"Infinity\\" aria-valuemin=\\"-Infinity\\" aria-valuenow=\\"\\" value=\\"\\"><!----><span class=\\"arco-input-suffix\\"><!----><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-info-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M24 20v14m0-16v-4m18 10c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path></svg><!----></span></span></div>
</div>"
`;

exports[`<input-number> demo: render [size] correctly 1`] = `
"<div class=\\"arco-space arco-space-vertical\\">
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 24px;\\"><span class=\\"arco-input-wrapper arco-input-number arco-input-number-mode-embed arco-input-number-size-large input-demo\\" style=\\"width: 320px;\\"><!----><input class=\\"arco-input arco-input-size-large\\" type=\\"text\\" placeholder=\\"Please Enter\\" role=\\"spinbutton\\" aria-valuemax=\\"Infinity\\" aria-valuemin=\\"-Infinity\\" aria-valuenow=\\"\\" value=\\"\\"><!----><span class=\\"arco-input-suffix\\"><!----><!----><div class=\\"arco-input-number-step\\"><button class=\\"arco-input-number-step-button\\" type=\\"button\\" tabindex=\\"-1\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-up\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 30.557 24.043 15 8.487 30.557\\"></path></svg></button><button class=\\"arco-input-number-step-button\\" type=\\"button\\" tabindex=\\"-1\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></button></div><!----></span></span></div>
  <!---->
  <div class=\\"arco-space-item\\"><span class=\\"arco-input-outer arco-input-outer-size-large arco-input-number arco-input-number-mode-button arco-input-number-size-large input-demo\\" style=\\"width: 320px;\\"><span class=\\"arco-input-prepend\\"><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-large arco-btn-status-normal arco-btn-only-icon arco-input-number-step-button\\" type=\\"button\\" tabindex=\\"-1\\"><span class=\\"arco-btn-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-minus\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M5 24h38\\"></path></svg></span></button></span><span class=\\"arco-input-wrapper\\"><!----><input class=\\"arco-input arco-input-size-large\\" type=\\"text\\" placeholder=\\"Please Enter\\" role=\\"spinbutton\\" aria-valuemax=\\"Infinity\\" aria-valuemin=\\"-Infinity\\" aria-valuenow=\\"\\" value=\\"\\"><!----><!----></span><span class=\\"arco-input-append\\"><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-large arco-btn-status-normal arco-btn-only-icon arco-input-number-step-button\\" type=\\"button\\" tabindex=\\"-1\\"><span class=\\"arco-btn-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-plus\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M5 24h38M24 5v38\\"></path></svg></span></button></span></span></div>
</div>"
`;

exports[`<input-number> demo: render [step-icon] correctly 1`] = `
"<div class=\\"arco-space arco-space-vertical\\">
  <!---->
  <div class=\\"arco-space-item\\"><span class=\\"arco-input-wrapper arco-input-number arco-input-number-mode-embed arco-input-number-size-medium\\" style=\\"width: 320px;\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" placeholder=\\"Please enter something\\" role=\\"spinbutton\\" aria-valuemax=\\"Infinity\\" aria-valuemin=\\"-Infinity\\" aria-valuenow=\\"\\" value=\\"\\"><!----><span class=\\"arco-input-suffix\\"><!----><!----><div class=\\"arco-input-number-step\\"><button class=\\"arco-input-number-step-button\\" type=\\"button\\" tabindex=\\"-1\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-plus\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M5 24h38M24 5v38\\"></path></svg></button><button class=\\"arco-input-number-step-button\\" type=\\"button\\" tabindex=\\"-1\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-minus\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M5 24h38\\"></path></svg></button></div><!----></span></span></div>
</div>"
`;
