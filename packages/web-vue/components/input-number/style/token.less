@import '../../style/theme/index.less';

@input-number-border-radius: @radius-small;
@input-number-step-layer-border-radius: 1px;

@input-number-size-mini-step-button-width: @size-mini;
@input-number-size-small-step-button-width: @size-small;
@input-number-size-default-step-button-width: @size-default;
@input-number-size-large-step-button-width: @size-large;

@input-number-step-button-color: var(~'@{arco-cssvars-prefix}-color-text-2');
@input-number-step-button-color_disabled: var(~'@{arco-cssvars-prefix}-color-text-4');
@input-number-step-button-color-border: var(~'@{arco-cssvars-prefix}-color-neutral-3');
@input-number-step-button-color-bg_default: var(~'@{arco-cssvars-prefix}-color-fill-2');
@input-number-step-button-color-bg_default_hover: var(~'@{arco-cssvars-prefix}-color-fill-3');
@input-number-step-button-color-bg_default_active: var(~'@{arco-cssvars-prefix}-color-fill-4');
@input-number-step-button-color-bg_disabled: var(~'@{arco-cssvars-prefix}-color-fill-2');
@input-number-step-button-color-bg_disabled_hover: var(~'@{arco-cssvars-prefix}-color-fill-2');
@input-number-step-button-color-bg_disabled_active: var(~'@{arco-cssvars-prefix}-color-fill-2');
