@import './token.less';
@import '../../input/style/token.less';

@input-prefix-cls: ~'@{prefix}-input';
@input-number-prefix-cls: ~'@{prefix}-input-number';

.@{input-number-prefix-cls} {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  border-radius: @input-number-border-radius;

  .transition() {
    transition: all @transition-duration-1 @transition-timing-function-linear;
  }

  &-step-button {
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    padding: 0;
    color: @input-number-step-button-color;
    background-color: @input-number-step-button-color-bg_default;
    cursor: pointer;
    user-select: none;
    .transition();

    &:hover {
      background-color: @input-number-step-button-color-bg_default_hover;
      border-color: $background-color;
    }

    &:active {
      background-color: @input-number-step-button-color-bg_default_active;
      border-color: $background-color;
    }

    &:disabled {
      color: @input-number-step-button-color_disabled;
      background-color: @input-number-step-button-color-bg_disabled;
      cursor: not-allowed;

      &:hover,
      &:active {
        background-color: @input-number-step-button-color-bg_disabled_hover;
        border-color: @input-number-step-button-color-border;
      }
    }
  }

  .@{input-prefix-cls}-wrapper {
    position: relative;
  }

  &-prefix,
  &-suffix {
    .transition();
  }

  // embed mode
  &-mode-embed {
    .@{input-number-prefix-cls}-step {
      position: absolute;
      top: 4px;
      right: 4px;
      bottom: 4px;
      width: 18px;
      overflow: hidden;
      border-radius: @input-number-step-layer-border-radius;
      opacity: 0;
      .transition();

      .@{input-number-prefix-cls}-step-button {
        width: 100%;
        height: 50%;
        font-size: 10px;
        border: none;
        border-color: @input-number-step-button-color-border;
      }
    }

    .@{input-prefix-cls}-suffix {
      justify-content: flex-end;
      min-width: 6px;
    }

    .@{input-prefix-cls}-suffix-has-feedback {
      min-width: 32px;

      .@{input-number-prefix-cls}-step {
        right: 30px;
      }
    }

    &:not(.@{input-prefix-cls}-disabled):not(.@{input-prefix-cls}-outer-disabled) {
      &:hover,
      &:focus-within {
        // 修正 hove 时存在 suffix 但隐藏了，视觉上 padding 过大问题
        .@{input-prefix-cls}-suffix:has(.@{input-number-prefix-cls}-suffix) {
          padding-left: @spacing-2;
        }

        & .@{input-number-prefix-cls}-step {
          opacity: 1;
        }

        & .@{input-number-prefix-cls}-suffix {
          opacity: 0;
          pointer-events: none;
        }
      }
    }

    // 修正 InputNumber 未激活 hover 时 StepButton 的 hover 背景色
    &.@{input-prefix-cls}-wrapper:not(.@{input-prefix-cls}-focus) {
      .@{input-number-prefix-cls}-step-button:not(.@{input-number-prefix-cls}-step-button-disabled):hover {
        background-color: @input-number-step-button-color-bg_default_active;
      }
    }
  }

  // button mode
  &-mode-button {
    .@{input-prefix-cls} {
      &-prepend,
      &-append {
        padding: 0;
        border: none;
      }

      &-prepend .@{input-number-prefix-cls}-step-button {
        border-right: @input-border-addon-separator-width solid transparent;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;

        &:not(&:active) {
          border-right-color: @input-number-step-button-color-border;
        }
      }

      &-append .@{input-number-prefix-cls}-step-button {
        border-left: @input-border-addon-separator-width solid transparent;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;

        &:not(&:active) {
          border-left-color: @input-number-step-button-color-border;
        }
      }
    }
  }

  &-readonly {
    .@{input-number-prefix-cls}-step-button {
      color: @input-number-step-button-color_disabled;
      pointer-events: none;
    }
  }
}
