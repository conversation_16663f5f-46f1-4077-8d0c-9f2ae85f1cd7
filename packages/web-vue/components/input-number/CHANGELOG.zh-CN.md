```yaml
changelog: true
```

## 2.56.3

`2024-10-25`

### 🐛 问题修复

- 修复左右按钮模式下只读属性不生效问题 ([#3314](https://github.com/arco-design/arco-design-vue/pull/3314))


## 2.56.0

`2024-07-26`

### 🆕 新增功能

- 增加 'keydown' 事件，可以禁止默认行为 ([#3248](https://github.com/arco-design/arco-design-vue/pull/3248))


## 2.54.6

`2024-03-01`

### 🐛 问题修复

- 修复步进按钮与 suffix/append 的样式重叠 ([#3005](https://github.com/arco-design/arco-design-vue/pull/3005))


## 2.54.5

`2024-02-21`

### 🐛 问题修复

- 修复双向绑定失效 ([#2961](https://github.com/arco-design/arco-design-vue/pull/2961))


## 2.54.4

`2024-02-02`

### 🐛 问题修复

- 修复 change 事件触发逻辑 ([#2915](https://github.com/arco-design/arco-design-vue/pull/2915))


## 2.52.0

`2023-09-22`

### 🆕 新增功能

- 增加 `input-attrs` 属性 ([#2716](https://github.com/arco-design/arco-design-vue/pull/2716))


## 2.50.2

`2023-08-25`

### 💎 功能优化

- 优化步长按钮的长按效果 ([#2668](https://github.com/arco-design/arco-design-vue/pull/2668))


## 2.49.0

`2023-07-21`

### 🆕 新增功能

- 增加数值操作自定义图标插槽 ([#2560](https://github.com/arco-design/arco-design-vue/pull/2560))


## 2.47.0

`2023-06-02`

### ⚠️ 重点注意

- `hide-button` 在 `mode="button"` 时也会生效 ([#2461](https://github.com/arco-design/arco-design-vue/pull/2461))


## 2.43.2

`2023-02-24`

### 🐛 问题修复

- 修复禁用状态下显示步长按钮 ([#2169](https://github.com/arco-design/arco-design-vue/pull/2169))

## 2.42.1

`2023-02-03`

### 🐛 问题修复

- 修复当最小/最大值改变时进步按钮不能正确禁用或启用的问题 ([#1777](https://github.com/arco-design/arco-design-vue/pull/1777))


## 2.34.0

`2022-07-29`

### 💎 功能优化

- 支持`read-only`属性 ([#1408](https://github.com/arco-design/arco-design-vue/pull/1408))


## 2.32.1

`2022-07-01`

### 🐛 问题修复

- 修复设定精度后会省略 '0' 的问题 ([#1368](https://github.com/arco-design/arco-design-vue/pull/1368))


## 2.32.0

`2022-06-24`

### 🐛 问题修复

- 修复在表单以外清除时的错误 ([#1329](https://github.com/arco-design/arco-design-vue/pull/1329))


## 2.29.1

`2022-06-02`

### 💎 功能优化

- 增加键盘事件，聚焦时显示步进按钮 ([#1224](https://github.com/arco-design/arco-design-vue/pull/1224))


## 2.29.0

`2022-05-27`

### 🐛 问题修复

- 修复 clear 没有触发 form 校验的问题 ([#1204](https://github.com/arco-design/arco-design-vue/pull/1204))


## 2.27.0

`2022-05-13`

### 🆕 新增功能

- 增加 modelEvent 属性和 input 事件 ([#1115](https://github.com/arco-design/arco-design-vue/pull/1115))


## 2.25.1

`2022-04-27`

### 💅 样式更新

- 修复清除按钮显示位置问题 ([#1048](https://github.com/arco-design/arco-design-vue/pull/1048))


## 2.25.0

`2022-04-22`

### 🐛 问题修复

- 修复 error 属性失效的问题 ([#1026](https://github.com/arco-design/arco-design-vue/pull/1026))


## 2.22.0

`2022-04-01`

### 🐛 问题修复

- 修复切换 `mode` 时展示错误的问题 ([#909](https://github.com/arco-design/arco-design-vue/pull/909))


## 2.14.3

`2022-01-12`

### 🐛 问题修复

- 修复输入时光标位置改变的问题 ([#552](https://github.com/arco-design/arco-design-vue/pull/552))


## 2.12.0

`2021-12-24`

### ⚠️ 重点注意

- 修改 change 事件仅在失焦和按下回车时触发，解决受控不能输入的问题 ([#452](https://github.com/arco-design/arco-design-vue/pull/452))

### 🆕 新增功能

- 增加对 input 插槽的支持 ([#451](https://github.com/arco-design/arco-design-vue/pull/451))


## 2.11.0

`2021-12-17`

### 🐛 问题修复

- 修复 `model-value` 为 0 时展示错误的问题 ([#407](https://github.com/arco-design/arco-design-vue/pull/407))


## 2.10.0

`2021-12-10`

### 🐛 问题修复

- 修复可重复输入 `-` 和 `.` 的问题 ([#359](https://github.com/arco-design/arco-design-vue/pull/359))


## 2.8.0

`2021-12-01`

### 🐛 问题修复

- 修复 button 模式下按钮没有添加 size 的问题 ([#293](https://github.com/arco-design/arco-design-vue/pull/293))


## 2.1.1

`2021-11-08`

### 🐛 问题修复

- 修复输入负号出错的问题 ([#114](https://github.com/arco-design/arco-design-vue/pull/114))


## 2.1.0

`2021-11-05`

### 🐛 问题修复

- 修复精度失效的问题 ([#93](https://github.com/arco-design/arco-design-vue/pull/93))

