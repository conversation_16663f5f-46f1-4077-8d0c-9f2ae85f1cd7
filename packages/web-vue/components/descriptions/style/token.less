@import '../../style/theme/index.less';

@descriptions-border-width: 1px;
@descriptions-border-style: solid;
@descriptions-color-border: var(~'@{arco-cssvars-prefix}-color-neutral-3');
@descriptions-border-radius: @radius-medium;
@descriptions-font-size-title: @font-size-title-1;

@descriptions-size-mini-title-margin-bottom: 6px;
@descriptions-size-small-title-margin-bottom: 8px;
@descriptions-size-medium-title-margin-bottom: 12px;
@descriptions-size-default-title-margin-bottom: 16px;
@descriptions-size-large-title-margin-bottom: 20px;

@descriptions-size-mini-font-size-text: @font-size-body-1;
@descriptions-size-small-font-size-text: @font-size-body-3;
@descriptions-size-medium-font-size-text: @font-size-body-3;
@descriptions-size-default-font-size-text: @font-size-body-3;
@descriptions-size-large-font-size-text: @font-size-body-3;

@descriptions-color-title: var(~'@{arco-cssvars-prefix}-color-text-1');
@descriptions-color-text-label: var(~'@{arco-cssvars-prefix}-color-text-3');
@descriptions-color-text-value: var(~'@{arco-cssvars-prefix}-color-text-1');
@descriptions-font-weight-title: @font-weight-500;
@descriptions-font-weight-text-label: @font-weight-500;
@descriptions-font-weight-text-value: @font-weight-400;

@descriptions-border-color-bg-label: var(~'@{arco-cssvars-prefix}-color-fill-1');

@descriptions-item-size-mini-spacing-bottom: 2px;
@descriptions-item-size-small-spacing-bottom: 4px;
@descriptions-item-size-medium-spacing-bottom: 8px;
@descriptions-item-size-default-spacing-bottom: 12px;
@descriptions-item-size-large-spacing-bottom: 16px;

@descriptions-border-item-size-mini-padding-horizontal: @spacing-8;
@descriptions-border-item-size-mini-padding-vertical: 3px;
@descriptions-border-item-size-small-padding-horizontal: @spacing-8;
@descriptions-border-item-size-small-padding-vertical: 3px;
@descriptions-border-item-size-medium-padding-horizontal: @spacing-8;
@descriptions-border-item-size-medium-padding-vertical: 5px;
@descriptions-border-item-size-default-padding-horizontal: @spacing-8;
@descriptions-border-item-size-default-padding-vertical: 7px;
@descriptions-border-item-size-large-padding-horizontal: @spacing-8;
@descriptions-border-item-size-large-padding-vertical: 9px;
