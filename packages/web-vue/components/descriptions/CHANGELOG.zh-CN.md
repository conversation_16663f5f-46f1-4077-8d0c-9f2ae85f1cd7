```yaml
changelog: true
```

## 2.57.0

`2025-03-10`

### 🐛 问题修复

- 修复 `span` 分配布局的错误使其更遵循用户配置 ([#3409](https://github.com/arco-design/arco-design-vue/pull/3409))


## 2.53.0

`2023-11-03`

### 🐛 问题修复

- 修复 `DescriptionsItem` 组件 `span` 属性失效的问题 ([#2765](https://github.com/arco-design/arco-design-vue/pull/2765))


## 2.39.0

`2022-11-18`


## 2.38.0-beta.1

`2022-10-14`

### 🆕 新增功能

- 描述中表格样式的 `layout-fixed`，当设置成 `fixed` 时，宽度会均分 ([#1670](https://github.com/arco-design/arco-design-vue/pull/1670))


## 2.20.0

`2022-03-18`

### 🆕 新增功能

- `column` 属性支持响应式配置 ([#839](https://github.com/arco-design/arco-design-vue/pull/839))


## 2.19.0

`2022-03-11`

### 🐛 问题修复

- 修复 `data` 为空数据时报错的问题 ([#812](https://github.com/arco-design/arco-design-vue/pull/812))


## 2.18.0-beta.2

`2022-02-25`

### 🆕 新增功能

- 增加 `descriptions-item` 组件 ([#739](https://github.com/arco-design/arco-design-vue/pull/739))


## 2.13.0

`2021-12-31`

### 🆕 新增功能

- `value` 和 `label` 插槽增加 index、data 参数 ([#470](https://github.com/arco-design/arco-design-vue/pull/470))


## 2.7.0

`2021-11-26`

### 🆕 新增功能

- 增加 `align` 属性 ([#268](https://github.com/arco-design/arco-design-vue/pull/268))

### 💅 样式更新

- 修改 value 区域样式，支持换行文本 ([#269](https://github.com/arco-design/arco-design-vue/pull/269))

