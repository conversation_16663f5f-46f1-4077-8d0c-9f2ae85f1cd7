## zh-CN
```yaml
meta:
  type: 组件
  category: 数据展示
title: 描述列表 Descriptions
description: 一般用于详情页的信息展示。
```
---
## en-US
```yaml
meta:
  type: Component
  category: Data Display
title: Descriptions
description: Generally used for the information display of the detail page.
```
---

@import ./__demo__/basic.md

@import ./__demo__/single.md

@import ./__demo__/align.md

@import ./__demo__/bordered.md

@import ./__demo__/layout.md

@import ./__demo__/example.md

## API

%%API(descriptions.tsx)%%

%%API(descriptions-item.vue)%%

%%INTERFACE(interface.ts)%%
