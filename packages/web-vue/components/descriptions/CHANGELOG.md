```yaml
changelog: true
```

## 2.57.0

`2025-03-10`

### 🐛 BugFix

- fixes the issue related to the layout distribution of span ([#3409](https://github.com/arco-design/arco-design-vue/pull/3409))


## 2.53.0

`2023-11-03`

### 🐛 BugFix

- Fix the problem of invalid `span` attribute of `DescriptionsItem` component ([#2765](https://github.com/arco-design/arco-design-vue/pull/2765))


## 2.39.0

`2022-11-18`


## 2.38.0-beta.1

`2022-10-14`

### 🆕 Feature

- The `layout-fixed` of the table style in the description. The width will be evenly distributed when it's set to `fixed`. ([#1670](https://github.com/arco-design/arco-design-vue/pull/1670))


## 2.20.0

`2022-03-18`

### 🆕 Feature

- The `column` property supports reactive configuration ([#839](https://github.com/arco-design/arco-design-vue/pull/839))


## 2.19.0

`2022-03-11`

### 🐛 BugFix

- Fix the problem of error when `data` is empty data ([#812](https://github.com/arco-design/arco-design-vue/pull/812))


## 2.18.0-beta.2

`2022-02-25`

### 🆕 Feature

- Added `descriptions-item` component ([#739](https://github.com/arco-design/arco-design-vue/pull/739))


## 2.13.0

`2021-12-31`

### 🆕 Feature

- The index and data parameters are added to the Value and Label slots ([#470](https://github.com/arco-design/arco-design-vue/pull/470))


## 2.7.0

`2021-11-26`

### 🆕 Feature

- Add the `align` prop ([#268](https://github.com/arco-design/arco-design-vue/pull/268))

### 💅 Style

- Modify the style of the value area to support line-wrapping text ([#269](https://github.com/arco-design/arco-design-vue/pull/269))

