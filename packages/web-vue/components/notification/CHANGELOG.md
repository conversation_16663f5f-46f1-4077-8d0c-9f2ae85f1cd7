```yaml
changelog: true
```

## 2.43.0

`2023-02-10`

### 🐛 BugFix

- Fix the problem that the update duration fails ([#2106](https://github.com/arco-design/arco-design-vue/pull/2106))


## 2.38.0-beta.1

`2022-10-14`

### 🆕 Feature

- notification and  notification listspace style adjustment ([#1676](https://github.com/arco-design/arco-design-vue/pull/1676))
- `showIcon` not working ([#1676](https://github.com/arco-design/arco-design-vue/pull/1676))
- support custom close icon & close element ([#1676](https://github.com/arco-design/arco-design-vue/pull/1676))
- support custom style ([#1676](https://github.com/arco-design/arco-design-vue/pull/1676))
- temove the notification corresponding to `id` ([#1676](https://github.com/arco-design/arco-design-vue/pull/1676))
- demo ([#1676](https://github.com/arco-design/arco-design-vue/pull/1676))


## 2.25.0

`2022-04-22`

### 🆕 Feature

- Add footer prop ([#1029](https://github.com/arco-design/arco-design-vue/pull/1029))


## 2.3.0

`2021-11-12`

### 🆕 Feature

- Added `onClose` callback method ([#149](https://github.com/arco-design/arco-design-vue/pull/149))

