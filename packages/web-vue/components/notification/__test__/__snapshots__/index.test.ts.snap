// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Notification should render notification 1`] = `
"<transition-group-stub name=\\"slide-right-notification\\" tag=\\"ul\\" appear=\\"false\\" persisted=\\"false\\" css=\\"true\\" class=\\"arco-notification-list arco-notification-list-top-right\\" style=\\"z-index: 0;\\">
  <li role=\\"alert\\" class=\\"arco-notification arco-notification-info\\">
    <div class=\\"arco-notification-left\\">
      <div class=\\"arco-notification-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-info-circle-fill\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
          <path fill-rule=\\"evenodd\\" clip-rule=\\"evenodd\\" d=\\"M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Zm2-30a1 1 0 0 0-1-1h-2a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1v-2Zm0 17h1a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-6a1 1 0 0 1-1-1v-2a1 1 0 0 1 1-1h1v-8a1 1 0 0 1-1-1v-2a1 1 0 0 1 1-1h3a1 1 0 0 1 1 1v11Z\\" fill=\\"currentColor\\" stroke=\\"none\\"></path>
        </svg></div>
    </div>
    <div class=\\"arco-notification-right\\">
      <!--v-if-->
      <div class=\\"arco-notification-content\\">Info Message</div>
      <!--v-if-->
    </div>
    <!--v-if-->
  </li>
  <li role=\\"alert\\" class=\\"arco-notification arco-notification-success\\">
    <div class=\\"arco-notification-left\\">
      <div class=\\"arco-notification-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-check-circle-fill\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
          <path fill-rule=\\"evenodd\\" clip-rule=\\"evenodd\\" d=\\"M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Zm10.207-24.379a1 1 0 0 0 0-1.414l-1.414-1.414a1 1 0 0 0-1.414 0L22 26.172l-4.878-4.88a1 1 0 0 0-1.415 0l-1.414 1.415a1 1 0 0 0 0 1.414l7 7a1 1 0 0 0 1.414 0l11.5-11.5Z\\" fill=\\"currentColor\\" stroke=\\"none\\"></path>
        </svg></div>
    </div>
    <div class=\\"arco-notification-right\\">
      <!--v-if-->
      <div class=\\"arco-notification-content\\">Success Message</div>
      <!--v-if-->
    </div>
    <!--v-if-->
  </li>
  <li role=\\"alert\\" class=\\"arco-notification arco-notification-warning\\">
    <div class=\\"arco-notification-left\\">
      <div class=\\"arco-notification-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-exclamation-circle-fill\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
          <path fill-rule=\\"evenodd\\" clip-rule=\\"evenodd\\" d=\\"M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Zm-2-11a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1h-2a1 1 0 0 0-1 1v2Zm4-18a1 1 0 0 0-1-1h-2a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1V15Z\\" fill=\\"currentColor\\" stroke=\\"none\\"></path>
        </svg></div>
    </div>
    <div class=\\"arco-notification-right\\">
      <!--v-if-->
      <div class=\\"arco-notification-content\\">Warning Message</div>
      <!--v-if-->
    </div>
    <!--v-if-->
  </li>
  <li role=\\"alert\\" class=\\"arco-notification arco-notification-error\\">
    <div class=\\"arco-notification-left\\">
      <div class=\\"arco-notification-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close-circle-fill\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
          <path fill-rule=\\"evenodd\\" clip-rule=\\"evenodd\\" d=\\"M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Zm4.955-27.771-4.95 4.95-4.95-4.95a1 1 0 0 0-1.414 0l-1.414 1.414a1 1 0 0 0 0 1.414l4.95 4.95-4.95 4.95a1 1 0 0 0 0 1.414l1.414 1.414a1 1 0 0 0 1.414 0l4.95-4.95 4.95 4.95a1 1 0 0 0 1.414 0l1.414-1.414a1 1 0 0 0 0-1.414l-4.95-4.95 4.95-4.95a1 1 0 0 0 0-1.414l-1.414-1.414a1 1 0 0 0-1.414 0Z\\" fill=\\"currentColor\\" stroke=\\"none\\"></path>
        </svg></div>
    </div>
    <div class=\\"arco-notification-right\\">
      <!--v-if-->
      <div class=\\"arco-notification-content\\">Error Message</div>
      <!--v-if-->
    </div>
    <!--v-if-->
  </li>
</transition-group-stub>"
`;
