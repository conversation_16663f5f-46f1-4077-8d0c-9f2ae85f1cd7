// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<notification> demo: render [basic] correctly 1`] = `
"<div class=\\"arco-space arco-space-horizontal arco-space-align-center\\">
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-right: 8px;\\"><button class=\\"arco-btn arco-btn-primary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
      <!--v-if--> Open Notification
    </button></div>
  <!---->
  <div class=\\"arco-space-item\\"><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
      <!--v-if--> Open Notification
    </button></div>
</div>"
`;

exports[`<notification> demo: render [btn] correctly 1`] = `
"<button class=\\"arco-btn arco-btn-primary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
  <!--v-if--> Open Notification
</button>"
`;

exports[`<notification> demo: render [custom-close] correctly 1`] = `
"<div class=\\"arco-space arco-space-horizontal arco-space-align-center\\">
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-right: 8px;\\"><button class=\\"arco-btn arco-btn-primary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
      <!--v-if--> Open Notification
    </button></div>
  <!---->
  <div class=\\"arco-space-item\\"><button class=\\"arco-btn arco-btn-primary arco-btn-shape-square arco-btn-size-medium arco-btn-status-danger\\" type=\\"button\\">
      <!--v-if--> Open Notification
    </button></div>
</div>"
`;

exports[`<notification> demo: render [position] correctly 1`] = `
"<div class=\\"arco-space arco-space-horizontal arco-space-align-center\\">
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-right: 8px;\\"><button class=\\"arco-btn arco-btn-primary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
      <!--v-if--> Top Right
    </button></div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-right: 8px;\\"><button class=\\"arco-btn arco-btn-primary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
      <!--v-if--> Top Left
    </button></div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-right: 8px;\\"><button class=\\"arco-btn arco-btn-primary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
      <!--v-if--> Bottom Right
    </button></div>
  <!---->
  <div class=\\"arco-space-item\\"><button class=\\"arco-btn arco-btn-primary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
      <!--v-if--> Bottom Left
    </button></div>
</div>"
`;

exports[`<notification> demo: render [style] correctly 1`] = `
"<button class=\\"arco-btn arco-btn-primary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
  <!--v-if--> Open Notification
</button>"
`;

exports[`<notification> demo: render [type] correctly 1`] = `
"<div class=\\"arco-space arco-space-horizontal arco-space-align-center\\">
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-right: 8px;\\"><button class=\\"arco-btn arco-btn-primary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
      <!--v-if--> Info
    </button></div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-right: 8px;\\"><button class=\\"arco-btn arco-btn-primary arco-btn-shape-square arco-btn-size-medium arco-btn-status-success\\" type=\\"button\\">
      <!--v-if--> Success
    </button></div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-right: 8px;\\"><button class=\\"arco-btn arco-btn-primary arco-btn-shape-square arco-btn-size-medium arco-btn-status-warning\\" type=\\"button\\">
      <!--v-if--> Warning
    </button></div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-right: 8px;\\"><button class=\\"arco-btn arco-btn-primary arco-btn-shape-square arco-btn-size-medium arco-btn-status-danger\\" type=\\"button\\">
      <!--v-if--> Error
    </button></div>
  <!---->
  <div class=\\"arco-space-item\\"><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
      <!--v-if--> Normal
    </button></div>
</div>"
`;

exports[`<notification> demo: render [update_duration] correctly 1`] = `
"<button class=\\"arco-btn arco-btn-primary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
  <!--v-if--> Open Notification
</button>"
`;

exports[`<notification> demo: render [update_notification] correctly 1`] = `
"<button class=\\"arco-btn arco-btn-primary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
  <!--v-if--> Open Notification
</button>"
`;
