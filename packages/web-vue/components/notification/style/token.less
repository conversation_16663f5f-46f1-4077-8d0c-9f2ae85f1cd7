@import '../../style/theme/index.less';

@notification-wrapper-margin-top: @spacing-8;
@notification-wrapper-margin-bottom: @spacing-8;
@notification-wrapper-margin-left: @spacing-8;
@notification-wrapper-margin-right: @spacing-8;

@notification-border-radius: @radius-medium;
@notification-margin-bottom: @spacing-8;
@notification-width: 300px;
@notification-padding-top: @spacing-8;
@notification-padding-bottom: @spacing-8;
@notification-padding-left: @spacing-8;
@notification-padding-right: @spacing-8;
@notification-font-size-icon: 24px;
@notification-font-size-title: @font-size-title-1;
@notification-font-size-content: @font-size-body-3;
@notification-icon-margin-right: @spacing-7;
@notification-title-margin-bottom: @spacing-2;
@notification-btn-wrapper-margin-top: @spacing-7;

@notification-border-width: 1px;
@notification-border-style: solid;

@notification-color-close-icon: var(~'@{arco-cssvars-prefix}-color-text-1');
@notification-close-icon-font-size: 12px;
@notification-close-icon-top: @spacing-6;
@notification-close-icon-right: @spacing-6;

@notification-normal-color-bg: var(~'@{arco-cssvars-prefix}-color-bg-popup');
@notification-normal-color-icon: var(~'@{arco-cssvars-prefix}-color-text-1');
@notification-normal-color-text-title: var(~'@{arco-cssvars-prefix}-color-text-1');
@notification-normal-color-text-content: var(~'@{arco-cssvars-prefix}-color-text-1');
@notification-normal-color-border: var(~'@{arco-cssvars-prefix}-color-neutral-3');

@notification-info-color-bg: var(~'@{arco-cssvars-prefix}-color-bg-popup');
@notification-info-color-icon: @color-primary-6;
@notification-info-color-text-title: var(~'@{arco-cssvars-prefix}-color-text-1');
@notification-info-color-text-content: var(~'@{arco-cssvars-prefix}-color-text-1');
@notification-info-color-border: var(~'@{arco-cssvars-prefix}-color-neutral-3');

@notification-success-color-bg: var(~'@{arco-cssvars-prefix}-color-bg-popup');
@notification-success-color-icon: @color-success-6;
@notification-success-color-text-title: var(~'@{arco-cssvars-prefix}-color-text-1');
@notification-success-color-text-content: var(~'@{arco-cssvars-prefix}-color-text-1');
@notification-success-color-border: var(~'@{arco-cssvars-prefix}-color-neutral-3');

@notification-warning-color-bg: var(~'@{arco-cssvars-prefix}-color-bg-popup');
@notification-warning-color-icon: @color-warning-6;
@notification-warning-color-text-title: var(~'@{arco-cssvars-prefix}-color-text-1');
@notification-warning-color-text-content: var(~'@{arco-cssvars-prefix}-color-text-1');
@notification-warning-color-border: var(~'@{arco-cssvars-prefix}-color-neutral-3');

@notification-error-color-bg: var(~'@{arco-cssvars-prefix}-color-bg-popup');
@notification-error-color-icon: @color-danger-6;
@notification-error-color-text-title: var(~'@{arco-cssvars-prefix}-color-text-1');
@notification-error-color-text-content: var(~'@{arco-cssvars-prefix}-color-text-1');
@notification-error-color-border: var(~'@{arco-cssvars-prefix}-color-neutral-3');
