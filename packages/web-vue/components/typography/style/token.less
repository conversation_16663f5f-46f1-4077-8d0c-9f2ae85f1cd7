@import '../../style/theme/index.less';

@typography-font-size-h1: 36px;
@typography-font-size-h2: 32px;
@typography-font-size-h3: 28px;
@typography-font-size-h4: 24px;
@typography-font-size-h5: 20px;
@typography-font-size-h6: 16px;

@typography-heading-font-weight: @font-weight-500;

@typography-color-text: var(~'@{arco-cssvars-prefix}-color-text-1');

@typography-text-color-text-primary: @color-primary-6;
@typography-text-color-text-secondary: var(~'@{arco-cssvars-prefix}-color-text-2');
@typography-text-color-text-success: @color-success-6;
@typography-text-color-text-warning: @color-warning-6;
@typography-text-color-text-error: @color-danger-6;
@typography-text-color-text_disabled: var(~'@{arco-cssvars-prefix}-color-text-4');

@typography-text-color-bg-mark: rgb(var(~'@{arco-cssvars-prefix}-yellow-4'));
@typography-text-font-weight-bold: @font-weight-500;

@typography-text-color-code: var(~'@{arco-cssvars-prefix}-color-text-2');
@typography-text-color-code-border: var(~'@{arco-cssvars-prefix}-color-neutral-3');
@typography-text-color-code-bg: var(~'@{arco-cssvars-prefix}-color-neutral-2');
@typography-text-padding-code-vertical: 2px;
@typography-text-padding-code-horizontal: 8px;
@typography-text-margin-code-horizontal: 2px;

@typography-paragraph-line-height: @line-height-base;
@typography-paragraph-line-height-close: 1.3;

// Operations
@typography-operation-margin-left: @spacing-1;
@typography-color-icon-copy: var(~'@{arco-cssvars-prefix}-color-text-2');
@typography-color-bg-icon-copy: @color-transparent;
@typography-color-icon-copy_hover: var(~'@{arco-cssvars-prefix}-color-text-2');
@typography-color-bg-icon-copy_hover: var(~'@{arco-cssvars-prefix}-color-fill-2');
@typography-color-icon-copy_copied: @color-success-6;

@typography-color-icon-edit: var(~'@{arco-cssvars-prefix}-color-text-2');
@typography-color-bg-icon-edit: @color-transparent;
@typography-color-icon-edit_hover: var(~'@{arco-cssvars-prefix}-color-text-2');
@typography-color-bg-icon-edit_hover: var(~'@{arco-cssvars-prefix}-color-fill-2');

@typography-color-expand-text: @color-primary-6;
@typography-color-expand-text_hover: @color-primary-5;

@typography-color-blockquote-border-width: 2px;
@typography-color-blockquote-border-left: var(~'@{arco-cssvars-prefix}-color-neutral-6');
@typography-color-blockquote-bg: var(~'@{arco-cssvars-prefix}-color-bg-2');

@typography-heading-margin-top: 1em;
@typography-heading-margin-bottom: 0.5em;
