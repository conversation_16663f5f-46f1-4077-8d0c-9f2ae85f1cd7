```yaml
title:
  zh-CN: 标题
  en-US: Title
```

## zh-CN

展示不同级别的标题。

---

## en-US

Show titles of different levels.

---

```vue
<template>
  <a-typography>
    <a-typography-title>
      H1. The Pragmatic Romanticism
    </a-typography-title>
    <a-typography-title :heading="2">
      H2. The Pragmatic Romanticism
    </a-typography-title>
    <a-typography-title :heading="3">
      H3. The Pragmatic Romanticism
    </a-typography-title>
    <a-typography-title :heading="4">
      H4. The Pragmatic Romanticism
    </a-typography-title>
    <a-typography-title :heading="5">
      H5. The Pragmatic Romanticism
    </a-typography-title>
    <a-typography-title :heading="6">
      H6. The Pragmatic Romanticism
    </a-typography-title>
  </a-typography>
</template>
```
