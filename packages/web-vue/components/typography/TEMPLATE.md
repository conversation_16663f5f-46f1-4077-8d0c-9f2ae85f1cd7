## zh-CN
```yaml
meta:
  type: 组件
  category: 通用
title: 排版 Typography
description: 用于展示标题、段落、文本内容。
```
---
## en-US
```yaml
meta:
  type: Component
  category: Common
title: Typography
description: Used to display titles, paragraphs, and text content.
```
---

@import ./__demo__/basic.md

@import ./__demo__/title.md

@import ./__demo__/text.md

@import ./__demo__/paragraph.md

@import ./__demo__/operations.md

@import ./__demo__/ellipsis.md

## API

%%API(typography.vue)%%

%%API(base.tsx)%%

%%API(title.tsx)%%

%%API(paragraph.tsx)%%

%%API(text.tsx)%%

%%INTERFACE(interface.ts)%%
