// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<typography> demo: render [basic] correctly 1`] = `
"<article class=\\"arco-typography\\" style=\\"margin-top: -40px;\\">
  <h1 class=\\"arco-typography\\"> Design system
    <!---->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </h1>
  <div class=\\"arco-typography\\"> A design is a plan or specification for the construction of an object or system or for the implementation of an activity or process, or the result of that plan or specification in the form of a prototype, product or process. The verb to design expresses the process of developing a design.
    <!---->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </div>
  <div class=\\"arco-typography\\"> In some cases, the direct construction of an object without an explicit prior plan (such as in craftwork, some engineering, coding, and graphic design) may also be considered <span class=\\"arco-typography\\"><b>to be a design activity.</b><!----><!--v-if--><!--v-if--><!--v-if--></span>
    <!---->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </div>
  <h2 class=\\"arco-typography\\">ArcoDesign
    <!---->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </h2>
  <div class=\\"arco-typography\\"> The ArcoDesign component library defines a set of default particle variables, and a custom theme is to <span class=\\"arco-typography\\"><mark>customize</mark><!----><!--v-if--><!--v-if--><!--v-if--></span> and <span class=\\"arco-typography\\"><u>overwrite</u><!----><!--v-if--><!--v-if--><!--v-if--></span> this variable list.
    <!---->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </div>
  <blockquote class=\\"arco-typography\\"> A design is a plan or specification for the construction of an object or system or for the implementation of an activity or process, or the result of that plan or specification in the form of a <span class=\\"arco-typography\\"><code>prototype</code><!----><!--v-if--><!--v-if--><!--v-if--></span>, <span class=\\"arco-typography\\"><code>product</code><!----><!--v-if--><!--v-if--><!--v-if--></span> or <span class=\\"arco-typography\\"><code>process</code><!----><!--v-if--><!--v-if--><!--v-if--></span>. The verb to design expresses the process of developing a design.
    <!---->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </blockquote>
  <div class=\\"arco-typography\\"><mark><del><u>A design is a plan or specification for the construction of an object or system or for the implementation of an activity or process.</u></del></mark>
    <!---->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </div>
  <div class=\\"arco-typography\\">
    <ul>
      <li> Architectural blueprints <ul>
          <li>Architectural blueprints</li>
        </ul>
      </li>
      <li>Engineering drawings</li>
      <li>Business processes</li>
    </ul>
    <!---->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </div>
  <div class=\\"arco-typography\\">
    <ol>
      <li>Architectural blueprints</li>
      <li>Engineering drawings</li>
      <li>Business processes</li>
    </ol>
    <!---->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </div>
</article>"
`;

exports[`<typography> demo: render [ellipsis] correctly 1`] = `
"<div>
  <h4 class=\\"arco-typography\\"> A design is a plan or specification for the construction of an object or system or for the implementation of an activity or process.
    <!---->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </h4>
  <div class=\\"arco-typography\\"> A design is a plan or specification for the construction of an object or system or for the implementation of an activity or process, or the result of that plan or specification in the form of a prototype, product or process. The verb to design expresses the process of developing a design. The verb to design expresses the process of developing a design.A design is a plan or specification for the construction of an object or system or for the implementation of an activity or process, or the result of that plan or specification in the form of a prototype, product or process. The verb to design expresses the process of developing a design. The verb to design expresses the process of developing a design.
    <!---->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </div>
  <div class=\\"arco-typography\\" style=\\"overflow: hidden; text-overflow: ellipsis; display: -webkit-box;\\"><span> A design is a plan or specification for the construction of an object or system or for the implementation of an activity or process, or the result of that plan or specification in the form of a prototype, product or process. The verb to design expresses the process of developing a design. The verb to design expresses the process of developing a design.A design is a plan or specification for the construction of an object or system or for the implementation of an activity or process, or the result of that plan or specification in the form of a prototype, product or process. The verb to design expresses the process of developing a design. The verb to design expresses the process of developing a design. </span></div>
  <div class=\\"arco-typography\\"> A design is a plan or specification for the construction of an object or system or for the implementation of an activity or process, or the result of that plan or specification in the form of a prototype, product or process. The verb to design expresses the process of developing a design. The verb to design expresses the process of developing a design.A design is a plan or specification for the construction of an object or system or for the implementation of an activity or process, or the result of that plan or specification in the form of a prototype, product or process. The verb to design expresses the process of developing a design. The verb to design expresses the process of developing a design.
    <!---->--Arco Design
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </div>
  <div class=\\"arco-typography\\"> A design is a plan or specification for the construction of an object or system or for the implementation of an activity or process, or the result of that plan or specification in the form of a prototype, product or process. The verb to design expresses the process of developing a design. The verb to design expresses the process of developing a design. A design is a plan or specification for the construction of an object or system or for the implementation of an activity or process, or the result of that plan or specification in the form of a prototype, product or process. The verb to design expresses the process of developing a design. The verb to design expresses the process of developing a design.
    <!---->--Arco Design
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </div>
</div>"
`;

exports[`<typography> demo: render [operations] correctly 1`] = `
"<article class=\\"arco-typography\\">
  <div class=\\"arco-typography\\"> Click the icon to copy this text.
    <!---->
    <!--v-if--><span class=\\"arco-typography-operation-copy\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-copy\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M20 6h18a2 2 0 0 1 2 2v22M8 16v24c0 1.105.891 2 1.996 2h20.007A1.99 1.99 0 0 0 32 40.008V15.997A1.997 1.997 0 0 0 30 14H10a2 2 0 0 0-2 2Z\\"></path></svg></span>
    <!---->
    <!--v-if-->
  </div>
  <div class=\\"arco-typography\\">Click the icon to edit this text.
    <!----><span class=\\"arco-typography-operation-edit\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-edit\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"m30.48 19.038 5.733-5.734a1 1 0 0 0 0-1.414l-5.586-5.586a1 1 0 0 0-1.414 0l-5.734 5.734m7 7L15.763 33.754a1 1 0 0 1-.59.286l-6.048.708a1 1 0 0 1-1.113-1.069l.477-6.31a1 1 0 0 1 .29-.631l14.7-14.7m7 7-7-7M6 42h36\\"></path></svg></span>
    <!---->
    <!--v-if-->
    <!--v-if-->
  </div>
</article>"
`;

exports[`<typography> demo: render [paragraph] correctly 1`] = `
"<article class=\\"arco-typography\\">
  <h5 class=\\"arco-typography\\">Default
    <!---->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </h5>
  <div class=\\"arco-typography\\"> A design is a plan or specification for the construction of an object or system or for the implementation of an activity or process, or the result of that plan or specification in the form of a prototype, product or process. The verb to design expresses the process of developing a design. In some cases, the direct construction of an object without an explicit prior plan (such as in craftwork, some engineering, coding, and graphic design) may also be considered to be a design activity.
    <!---->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </div>
  <h5 class=\\"arco-typography\\">Secondary
    <!---->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </h5>
  <div class=\\"arco-typography arco-typography-secondary\\"> A design is a plan or specification for the construction of an object or system or for the implementation of an activity or process, or the result of that plan or specification in the form of a prototype, product or process. The verb to design expresses the process of developing a design. In some cases, the direct construction of an object without an explicit prior plan (such as in craftwork, some engineering, coding, and graphic design) may also be considered to be a design activity.
    <!---->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </div>
  <h5 class=\\"arco-typography\\">Spacing default
    <!---->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </h5>
  <div class=\\"arco-typography\\"> A design is a plan or specification for the construction of an object or system or for the implementation of an activity or process, or the result of that plan or specification in the form of a prototype, product or process. The verb to design expresses the process of developing a design. In some cases, the direct construction of an object without an explicit prior plan (such as in craftwork, some engineering, coding, and graphic design) may also be considered to be a design activity.
    <!---->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </div>
  <h5 class=\\"arco-typography\\">Spacing close
    <!---->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </h5>
  <div class=\\"arco-typography arco-typography-secondary arco-typography-spacing-close\\"> A design is a plan or specification for the construction of an object or system or for the implementation of an activity or process, or the result of that plan or specification in the form of a prototype, product or process. The verb to design expresses the process of developing a design.
    <!---->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </div>
</article>"
`;

exports[`<typography> demo: render [text] correctly 1`] = `
"<div class=\\"arco-space arco-space-vertical\\">
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 10px;\\"><span class=\\"arco-typography\\"> Arco Design <!----><!--v-if--><!--v-if--><!--v-if--></span></div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 10px;\\"><span class=\\"arco-typography arco-typography-secondary\\"> Secondary <!----><!--v-if--><!--v-if--><!--v-if--></span></div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 10px;\\"><span class=\\"arco-typography arco-typography-primary\\"> Primary <!----><!--v-if--><!--v-if--><!--v-if--></span></div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 10px;\\"><span class=\\"arco-typography arco-typography-success\\"> Success <!----><!--v-if--><!--v-if--><!--v-if--></span></div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 10px;\\"><span class=\\"arco-typography arco-typography-warning\\"> Warning <!----><!--v-if--><!--v-if--><!--v-if--></span></div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 10px;\\"><span class=\\"arco-typography arco-typography-danger\\"> Danger <!----><!--v-if--><!--v-if--><!--v-if--></span></div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 10px;\\"><span class=\\"arco-typography\\"><b> Bold </b><!----><!--v-if--><!--v-if--><!--v-if--></span></div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 10px;\\"><span class=\\"arco-typography arco-typography-disabled\\"> Disabled <!----><!--v-if--><!--v-if--><!--v-if--></span></div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 10px;\\"><span class=\\"arco-typography\\"><mark> Mark </mark><!----><!--v-if--><!--v-if--><!--v-if--></span></div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 10px;\\"><span class=\\"arco-typography\\"><u> Underline </u><!----><!--v-if--><!--v-if--><!--v-if--></span></div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 10px;\\"><span class=\\"arco-typography\\"><del> Line through </del><!----><!--v-if--><!--v-if--><!--v-if--></span></div>
  <!---->
  <div class=\\"arco-space-item\\"><span class=\\"arco-typography\\"><code> Code snippet </code><!----><!--v-if--><!--v-if--><!--v-if--></span></div>
</div>"
`;

exports[`<typography> demo: render [title] correctly 1`] = `
"<article class=\\"arco-typography\\">
  <h1 class=\\"arco-typography\\"> H1. The Pragmatic Romanticism
    <!---->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </h1>
  <h2 class=\\"arco-typography\\"> H2. The Pragmatic Romanticism
    <!---->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </h2>
  <h3 class=\\"arco-typography\\"> H3. The Pragmatic Romanticism
    <!---->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </h3>
  <h4 class=\\"arco-typography\\"> H4. The Pragmatic Romanticism
    <!---->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </h4>
  <h5 class=\\"arco-typography\\"> H5. The Pragmatic Romanticism
    <!---->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </h5>
  <h6 class=\\"arco-typography\\"> H6. The Pragmatic Romanticism
    <!---->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </h6>
</article>"
`;
