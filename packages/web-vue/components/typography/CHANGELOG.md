```yaml
changelog: true
```

## 2.51.0

`2023-09-01`

### 🐛 BugFix

- Fix the problem of missing related component styles when importing on demand ([#2682](https://github.com/arco-design/arco-design-vue/pull/2682))


## 2.49.2

`2023-07-28`

### 🐛 BugFix

- enhanced typography styles for handling long words ([#2583](https://github.com/arco-design/arco-design-vue/pull/2583))


## 2.41.0

`2022-12-30`

### 🐛 BugFix

- Fixed ellipsis invalidation with parent container setting `white-space: nowrap` ([#1995](https://github.com/arco-design/arco-design-vue/pull/1995))


## 2.37.0

`2022-09-16`

### 🆕 Feature

- Omit mode adds support for CSS mode (experimental) ([#1635](https://github.com/arco-design/arco-design-vue/pull/1635))


## 2.33.1

`2022-07-22`

### 🐛 BugFix

- cancel click propagation of suffix icon ([#1411](https://github.com/arco-design/arco-design-vue/pull/1411))


## 2.32.0

`2022-06-24`

### 🆕 Feature

- Added `tooltip-props` class property ([#1338](https://github.com/arco-design/arco-design-vue/pull/1338))


## 2.27.0

`2022-05-13`

### 🐛 BugFix

- fix the problem that event `edit-end` triggered twice when press enter ([#1122](https://github.com/arco-design/arco-design-vue/pull/1122))


## 2.24.0

`2022-04-15`

### 🐛 BugFix

- fixed the warning of `slots.default` ([#980](https://github.com/arco-design/arco-design-vue/pull/980))


## 2.22.0

`2022-04-01`

### 🐛 BugFix

- fix the problem that copy does not work ([#915](https://github.com/arco-design/arco-design-vue/pull/915))


## 2.20.0

`2022-03-18`

### 🐛 BugFix

- Fix `ellipsisText` not updating ([#833](https://github.com/arco-design/arco-design-vue/pull/833))


## 2.16.0

`2022-01-21`

### 🆕 Feature

- add property `copy-delay` support customize the delay time for the disappearance of copy success status ([#632](https://github.com/arco-design/arco-design-vue/pull/632))

### 🐛 BugFix

- Fix the problem of warning in JSX usage ([#591](https://github.com/arco-design/arco-design-vue/pull/591))


## 2.2.0

`2021-11-10`

### 🐛 BugFix

- Fixed the problem of unable input ([#121](https://github.com/arco-design/arco-design-vue/pull/121))

