```yaml
changelog: true
```

## 2.51.0

`2023-09-01`

### 🐛 问题修复

- 修复按需导入时缺少相关组件样式的问题 ([#2682](https://github.com/arco-design/arco-design-vue/pull/2682))


## 2.49.2

`2023-07-28`

### 🐛 问题修复

- 增强对连续长单词的省略支持 ([#2583](https://github.com/arco-design/arco-design-vue/pull/2583))


## 2.41.0

`2022-12-30`

### 🐛 问题修复

- 修复父容器设置`white-space: nowrap`时 ellipsis 失效 ([#1995](https://github.com/arco-design/arco-design-vue/pull/1995))


## 2.37.0

`2022-09-16`

### 🆕 新增功能

- 省略模式增加支持 CSS 方式（实验性） ([#1635](https://github.com/arco-design/arco-design-vue/pull/1635))


## 2.33.1

`2022-07-22`

### 🐛 问题修复

- 后缀 icon 点击事件取消冒泡 ([#1411](https://github.com/arco-design/arco-design-vue/pull/1411))


## 2.32.0

`2022-06-24`

### 🆕 新增功能

- 新增 `tooltip-props` 类属性 ([#1338](https://github.com/arco-design/arco-design-vue/pull/1338))


## 2.27.0

`2022-05-13`

### 🐛 问题修复

- 修复按下回车的时候触发两次 `editEnd` 的问题 ([#1122](https://github.com/arco-design/arco-design-vue/pull/1122))


## 2.24.0

`2022-04-15`

### 🐛 问题修复

- 解决 `slots.default` 报警告的问题 ([#980](https://github.com/arco-design/arco-design-vue/pull/980))


## 2.22.0

`2022-04-01`

### 🐛 问题修复

- 修复复制无效的问题 ([#915](https://github.com/arco-design/arco-design-vue/pull/915))


## 2.20.0

`2022-03-18`

### 🐛 问题修复

- 修复 `ellipsisText` 未更新的问题 ([#833](https://github.com/arco-design/arco-design-vue/pull/833))


## 2.16.0

`2022-01-21`

### 🆕 新增功能

- 新增属性 `copy-delay` 用于自定义复制成功状态消失的延迟时间 ([#632](https://github.com/arco-design/arco-design-vue/pull/632))

### 🐛 问题修复

- 修复 JSX 使用中存在警告的问题 ([#591](https://github.com/arco-design/arco-design-vue/pull/591))


## 2.2.0

`2021-11-10`

### 🐛 问题修复

- 修复无法输入的问题 ([#121](https://github.com/arco-design/arco-design-vue/pull/121))

