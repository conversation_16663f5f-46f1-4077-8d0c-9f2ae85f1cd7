## zh-CN
```yaml
meta:
  type: 组件
  category: 数据输入
title: 颜色选择器 ColorPicker
description: 用于选择和展示颜色
```
---
## en-US
```yaml
meta:
  type: Component
  category: Data Entry
title: ColorPicker
description: Used for select and display colors.
```
---

@import ./__demo__/basic.md
@import ./__demo__/size.md
@import ./__demo__/disabled.md
@import ./__demo__/format.md
@import ./__demo__/colors.md
@import ./__demo__/trigger.md
@import ./__demo__/trigger-element.md
@import ./__demo__/only-panel.md

## API

%%API(color-picker.tsx)%%
