```yaml
title:
  zh-CN: 尺寸
  en-US: Size
```

## zh-CN

颜色选择器定义了四种尺寸（`mini`,`small`, `medium`, `large`），分别为 24px，28px，32px，36px。

---

## en-US

ColorPicker defines four sizes (`mini`, `small`, `medium`, `large`), which are 24px, 28px, 32px, and 36px.

---

```vue
<template>
  <a-space>
    <a-color-picker defaultValue="#165DFF" size="mini" />
    <a-color-picker defaultValue="#165DFF" size="small" />
    <a-color-picker defaultValue="#165DFF" size="medium" />
    <a-color-picker defaultValue="#165DFF" size="large" />
  </a-space>
</template>
```
