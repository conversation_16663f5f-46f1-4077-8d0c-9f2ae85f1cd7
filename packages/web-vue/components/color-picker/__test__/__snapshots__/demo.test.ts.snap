// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<color-picker> demo: render [basic] correctly 1`] = `
"<div class=\\"arco-space arco-space-horizontal arco-space-align-center\\">
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-right: 8px;\\">
    <div class=\\"arco-color-picker arco-color-picker-size-medium\\">
      <div class=\\"arco-color-picker-preview\\" style=\\"background-color: rgb(22, 93, 255);\\"></div>
      <!----><input class=\\"arco-color-picker-input\\" value=\\"#165DFF\\">
    </div>
    <!---->
  </div>
  <!---->
  <div class=\\"arco-space-item\\">
    <div class=\\"arco-color-picker arco-color-picker-size-medium\\">
      <div class=\\"arco-color-picker-preview\\" style=\\"background-color: rgb(22, 93, 255);\\"></div>
      <div class=\\"arco-color-picker-value\\">#165DFF</div><input class=\\"arco-color-picker-input\\" value=\\"#165DFF\\">
    </div>
    <!---->
  </div>
</div>"
`;

exports[`<color-picker> demo: render [colors] correctly 1`] = `
"<div class=\\"arco-color-picker arco-color-picker-size-medium\\">
  <div class=\\"arco-color-picker-preview\\" style=\\"background-color: rgb(22, 93, 255);\\"></div>
  <!----><input class=\\"arco-color-picker-input\\" value=\\"#165DFF\\">
</div>"
`;

exports[`<color-picker> demo: render [disabled] correctly 1`] = `
"<div class=\\"arco-space arco-space-horizontal arco-space-align-center\\">
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-right: 8px;\\">
    <div class=\\"arco-color-picker arco-color-picker-size-medium arco-color-picker-disabled\\">
      <div class=\\"arco-color-picker-preview\\" style=\\"background-color: rgb(22, 93, 255);\\"></div>
      <!----><input class=\\"arco-color-picker-input\\" disabled=\\"\\" value=\\"#165DFF\\">
    </div>
    <!---->
  </div>
  <!---->
  <div class=\\"arco-space-item\\">
    <div class=\\"arco-color-picker arco-color-picker-size-medium arco-color-picker-disabled\\">
      <div class=\\"arco-color-picker-preview\\" style=\\"background-color: rgb(22, 93, 255);\\"></div>
      <div class=\\"arco-color-picker-value\\">#165DFF</div><input class=\\"arco-color-picker-input\\" disabled=\\"\\" value=\\"#165DFF\\">
    </div>
    <!---->
  </div>
</div>"
`;

exports[`<color-picker> demo: render [format] correctly 1`] = `
"<div class=\\"arco-space arco-space-vertical\\">
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 8px;\\"><span class=\\"arco-radio-group-button arco-radio-group-size-medium arco-radio-group-direction-horizontal\\"><label class=\\"arco-radio-button arco-radio-checked\\"><input type=\\"radio\\" checked=\\"\\" class=\\"arco-radio-target\\" value=\\"hex\\"><span class=\\"arco-radio-button-content\\">hex</span></label><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"rgb\\"><span class=\\"arco-radio-button-content\\">rgb</span></label></span></div>
  <!---->
  <div class=\\"arco-space-item\\">
    <div class=\\"arco-color-picker arco-color-picker-size-medium\\">
      <div class=\\"arco-color-picker-preview\\" style=\\"background-color: rgb(22, 93, 255);\\"></div>
      <div class=\\"arco-color-picker-value\\">#165DFF</div><input class=\\"arco-color-picker-input\\" value=\\"#165DFF\\">
    </div>
    <!---->
  </div>
</div>"
`;

exports[`<color-picker> demo: render [only-panel] correctly 1`] = `
"<div class=\\"arco-space arco-space-horizontal arco-space-align-center\\">
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-right: 32px;\\">
    <div class=\\"arco-color-picker-panel\\">
      <div class=\\"arco-color-picker-palette\\" style=\\"background-color: rgb(0, 78, 255);\\">
        <div class=\\"arco-color-picker-handler\\" style=\\"top: 0%; left: 91.37254901960785%;\\"></div>
      </div>
      <div class=\\"arco-color-picker-panel-control\\">
        <div class=\\"arco-color-picker-control-wrapper\\">
          <div>
            <div class=\\"arco-color-picker-control-bar arco-color-picker-control-bar-hue\\">
              <div class=\\"arco-color-picker-handler\\" style=\\"left: 61.587982832618025%; color: rgb(22, 93, 255);\\"></div>
            </div>
            <div class=\\"arco-color-picker-control-bar-bg\\">
              <div class=\\"arco-color-picker-control-bar arco-color-picker-control-bar-alpha\\">
                <div class=\\"arco-color-picker-handler\\" style=\\"left: 100%; color: rgb(22, 93, 255);\\"></div>
              </div>
            </div>
          </div>
          <div class=\\"arco-color-picker-preview\\" style=\\"background-color: rgb(22, 93, 255);\\"></div>
        </div>
        <div class=\\"arco-color-picker-input-wrapper\\"><span class=\\"arco-select-view-single arco-select arco-color-picker-select arco-select-view arco-select-view-size-mini\\" title=\\"Hex\\"><!----><input class=\\"arco-select-view-input arco-select-view-input-hidden\\" readonly=\\"\\" value=\\"\\"><span class=\\"arco-select-view-value\\">Hex</span><span class=\\"arco-select-view-suffix\\"><!----><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down arco-select-view-arrow-icon\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></span>
          <!----></span></span>
          <!---->
          <div class=\\"arco-color-picker-group-wrapper\\">
            <div class=\\"arco-input-group arco-color-picker-input-group\\"><span class=\\"arco-input-wrapper arco-color-picker-input-hex\\"><span class=\\"arco-input-prefix\\">#</span><input class=\\"arco-input arco-input-size-mini\\" type=\\"text\\" value=\\"165DFF\\">
              <!---->
              <!----></span><span class=\\"arco-input-wrapper arco-input-number arco-input-number-mode-embed arco-input-number-size-mini arco-color-picker-input-alpha\\"><!----><input class=\\"arco-input arco-input-size-mini\\" type=\\"text\\" role=\\"spinbutton\\" aria-valuemax=\\"100\\" aria-valuemin=\\"0\\" aria-valuenow=\\"100\\" value=\\"100\\"><!----><span class=\\"arco-input-suffix\\"><!----><div class=\\"arco-input-number-suffix\\">%</div><div class=\\"arco-input-number-step\\"><button class=\\"arco-input-number-step-button arco-input-number-step-button-disabled\\" type=\\"button\\" tabindex=\\"-1\\" disabled=\\"\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-up\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 30.557 24.043 15 8.487 30.557\\"></path></svg></button><button class=\\"arco-input-number-step-button\\" type=\\"button\\" tabindex=\\"-1\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></button></div><!----></span></span>
            </div>
          </div>
        </div>
      </div>
      <div class=\\"arco-color-picker-panel-colors\\">
        <div class=\\"arco-color-picker-colors-section\\">
          <div class=\\"arco-color-picker-colors-text\\">最近使用颜色</div>
          <div class=\\"arco-color-picker-colors-wrapper\\"><span class=\\"arco-color-picker-colors-empty\\">暂无</span></div>
        </div>
        <div class=\\"arco-color-picker-colors-section\\">
          <div class=\\"arco-color-picker-colors-text\\">系统预设颜色</div>
          <div class=\\"arco-color-picker-colors-wrapper\\">
            <div class=\\"arco-color-picker-colors-list\\">
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(0, 180, 42);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(0, 180, 42);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(60, 126, 255);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(60, 126, 255);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(255, 125, 0);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(255, 125, 0);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(247, 105, 101);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(247, 105, 101);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(247, 186, 30);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(247, 186, 30);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(245, 49, 157);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(245, 49, 157);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(217, 26, 217);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(217, 26, 217);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(159, 219, 29);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(159, 219, 29);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(250, 220, 25);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(250, 220, 25);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(114, 46, 209);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(114, 46, 209);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(52, 145, 250);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(52, 145, 250);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(123, 225, 136);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(123, 225, 136);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(147, 190, 255);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(147, 190, 255);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(255, 207, 139);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(255, 207, 139);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(251, 176, 167);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(251, 176, 167);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(252, 233, 150);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(252, 233, 150);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(251, 157, 199);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(251, 157, 199);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(240, 142, 230);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(240, 142, 230);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(220, 241, 144);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(220, 241, 144);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(253, 250, 148);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(253, 250, 148);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(195, 150, 237);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(195, 150, 237);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(159, 212, 253);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(159, 212, 253);\\"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!---->
  <div class=\\"arco-space-item\\">
    <div class=\\"arco-color-picker-panel arco-color-picker-panel-disabled\\">
      <div class=\\"arco-color-picker-palette\\" style=\\"background-color: rgb(0, 255, 205);\\">
        <div class=\\"arco-color-picker-handler\\" style=\\"top: 17.647058823529417%; left: 91.42857142857143%;\\"></div>
      </div>
      <div class=\\"arco-color-picker-panel-control\\">
        <div class=\\"arco-color-picker-control-wrapper\\">
          <div>
            <div class=\\"arco-color-picker-control-bar arco-color-picker-control-bar-hue\\">
              <div class=\\"arco-color-picker-handler\\" style=\\"left: 46.70138888888889%; color: rgb(18, 210, 172);\\"></div>
            </div>
            <div class=\\"arco-color-picker-control-bar-bg\\">
              <div class=\\"arco-color-picker-control-bar arco-color-picker-control-bar-alpha\\">
                <div class=\\"arco-color-picker-handler\\" style=\\"left: 100%; color: rgb(18, 210, 172);\\"></div>
              </div>
            </div>
          </div>
          <div class=\\"arco-color-picker-preview\\" style=\\"background-color: rgb(18, 210, 172);\\"></div>
        </div>
        <div class=\\"arco-color-picker-input-wrapper\\"><span class=\\"arco-select-view-single arco-select arco-color-picker-select arco-select-view arco-select-view-size-mini\\" title=\\"Hex\\"><!----><input class=\\"arco-select-view-input arco-select-view-input-hidden\\" readonly=\\"\\" value=\\"\\"><span class=\\"arco-select-view-value\\">Hex</span><span class=\\"arco-select-view-suffix\\"><!----><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down arco-select-view-arrow-icon\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></span>
          <!----></span></span>
          <!---->
          <div class=\\"arco-color-picker-group-wrapper\\">
            <div class=\\"arco-input-group arco-color-picker-input-group\\"><span class=\\"arco-input-wrapper arco-input-disabled arco-color-picker-input-hex\\"><span class=\\"arco-input-prefix\\">#</span><input class=\\"arco-input arco-input-size-mini\\" type=\\"text\\" disabled=\\"\\" value=\\"12D2AC\\">
              <!---->
              <!----></span><span class=\\"arco-input-wrapper arco-input-disabled arco-input-number arco-input-number-mode-embed arco-input-number-size-mini arco-color-picker-input-alpha\\"><!----><input class=\\"arco-input arco-input-size-mini\\" type=\\"text\\" disabled=\\"\\" role=\\"spinbutton\\" aria-valuemax=\\"100\\" aria-valuemin=\\"0\\" aria-valuenow=\\"100\\" value=\\"100\\"><!----><span class=\\"arco-input-suffix\\"><!----><div class=\\"arco-input-number-suffix\\">%</div><div class=\\"arco-input-number-step\\"><button class=\\"arco-input-number-step-button arco-input-number-step-button-disabled\\" type=\\"button\\" tabindex=\\"-1\\" disabled=\\"\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-up\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 30.557 24.043 15 8.487 30.557\\"></path></svg></button><button class=\\"arco-input-number-step-button arco-input-number-step-button-disabled\\" type=\\"button\\" tabindex=\\"-1\\" disabled=\\"\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></button></div><!----></span></span>
            </div>
          </div>
        </div>
      </div>
      <div class=\\"arco-color-picker-panel-colors\\">
        <!---->
        <div class=\\"arco-color-picker-colors-section\\">
          <div class=\\"arco-color-picker-colors-text\\">系统预设颜色</div>
          <div class=\\"arco-color-picker-colors-wrapper\\">
            <div class=\\"arco-color-picker-colors-list\\">
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(0, 180, 42);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(0, 180, 42);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(60, 126, 255);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(60, 126, 255);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(255, 125, 0);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(255, 125, 0);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(247, 105, 101);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(247, 105, 101);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(247, 186, 30);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(247, 186, 30);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(245, 49, 157);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(245, 49, 157);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(217, 26, 217);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(217, 26, 217);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(159, 219, 29);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(159, 219, 29);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(250, 220, 25);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(250, 220, 25);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(114, 46, 209);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(114, 46, 209);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(52, 145, 250);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(52, 145, 250);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(123, 225, 136);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(123, 225, 136);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(147, 190, 255);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(147, 190, 255);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(255, 207, 139);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(255, 207, 139);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(251, 176, 167);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(251, 176, 167);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(252, 233, 150);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(252, 233, 150);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(251, 157, 199);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(251, 157, 199);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(240, 142, 230);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(240, 142, 230);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(220, 241, 144);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(220, 241, 144);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(253, 250, 148);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(253, 250, 148);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(195, 150, 237);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(195, 150, 237);\\"></div>
              </div>
              <div class=\\"arco-color-picker-color-block\\" style=\\"background-color: rgb(159, 212, 253);\\">
                <div class=\\"arco-color-picker-block\\" style=\\"background-color: rgb(159, 212, 253);\\"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>"
`;

exports[`<color-picker> demo: render [size] correctly 1`] = `
"<div class=\\"arco-space arco-space-horizontal arco-space-align-center\\">
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-right: 8px;\\">
    <div class=\\"arco-color-picker arco-color-picker-size-mini\\">
      <div class=\\"arco-color-picker-preview\\" style=\\"background-color: rgb(22, 93, 255);\\"></div>
      <!----><input class=\\"arco-color-picker-input\\" value=\\"#165DFF\\">
    </div>
    <!---->
  </div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-right: 8px;\\">
    <div class=\\"arco-color-picker arco-color-picker-size-small\\">
      <div class=\\"arco-color-picker-preview\\" style=\\"background-color: rgb(22, 93, 255);\\"></div>
      <!----><input class=\\"arco-color-picker-input\\" value=\\"#165DFF\\">
    </div>
    <!---->
  </div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-right: 8px;\\">
    <div class=\\"arco-color-picker arco-color-picker-size-medium\\">
      <div class=\\"arco-color-picker-preview\\" style=\\"background-color: rgb(22, 93, 255);\\"></div>
      <!----><input class=\\"arco-color-picker-input\\" value=\\"#165DFF\\">
    </div>
    <!---->
  </div>
  <!---->
  <div class=\\"arco-space-item\\">
    <div class=\\"arco-color-picker arco-color-picker-size-large\\">
      <div class=\\"arco-color-picker-preview\\" style=\\"background-color: rgb(22, 93, 255);\\"></div>
      <!----><input class=\\"arco-color-picker-input\\" value=\\"#165DFF\\">
    </div>
    <!---->
  </div>
</div>"
`;

exports[`<color-picker> demo: render [trigger] correctly 1`] = `
"<div class=\\"arco-space arco-space-vertical\\">
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 8px;\\"><button type=\\"button\\" role=\\"switch\\" aria-checked=\\"false\\" class=\\"arco-switch arco-switch-type-circle\\"><span class=\\"arco-switch-handle\\"><span class=\\"arco-switch-handle-icon\\"></span></span><!--  prettier-ignore  --><span class=\\"arco-switch-text-holder\\">OFF</span><span class=\\"arco-switch-text\\">OFF</span></button></div>
  <!---->
  <div class=\\"arco-space-item\\">
    <div class=\\"arco-color-picker arco-color-picker-size-medium\\">
      <div class=\\"arco-color-picker-preview\\" style=\\"background-color: rgb(22, 93, 255);\\"></div>
      <!----><input class=\\"arco-color-picker-input\\" value=\\"#165DFF\\">
    </div>
    <!---->
  </div>
</div>"
`;

exports[`<color-picker> demo: render [trigger-element] correctly 1`] = `
"<div class=\\"arco-space arco-space-horizontal arco-space-align-center\\">
  <!---->
  <div class=\\"arco-space-item\\"><span class=\\"arco-tag arco-tag-size-medium arco-tag-checked arco-tag-custom-color\\" style=\\"background-color: rgb(22, 93, 255);\\"><span class=\\"arco-tag-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-bg-colors\\" style=\\"color: rgb(255, 255, 255);\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"m9.442 25.25 10.351 10.765a1 1 0 0 0 1.428.014L32 25.25H9.442Z\\" fill=\\"currentColor\\" stroke=\\"none\\"></path><path d=\\"M19 5.25 22.75 9m0 0 12.043 12.043a1 1 0 0 1 0 1.414L32 25.25M22.75 9 8.693 23.057a1 1 0 0 0-.013 1.4l.762.793m0 0 10.351 10.765a1 1 0 0 0 1.428.014L32 25.25m-22.558 0H32M6 42h36\\"></path><path fill-rule=\\"evenodd\\" clip-rule=\\"evenodd\\" d=\\"M40.013 29.812 37.201 27l-2.812 2.812a4 4 0 1 0 5.624 0Z\\" fill=\\"currentColor\\" stroke=\\"none\\"></path></svg></span> #165DFF
    <!--v-if-->
    <!--v-if--></span>
    <!---->
  </div>
</div>"
`;
