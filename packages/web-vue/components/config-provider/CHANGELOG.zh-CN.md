```yaml
changelog: true
```

## 2.48.0

`2023-06-30`

### 🆕 新增功能

- 增加 `exchangeTime` 属性 ([#2529](https://github.com/arco-design/arco-design-vue/pull/2529))


## 2.47.0

`2023-06-02`

### 🆕 新增功能

- empty slots 增加component 属性 ([#2448](https://github.com/arco-design/arco-design-vue/pull/2448))


## 2.28.0

`2022-05-20`

### 🆕 新增功能

- 增加 `empty` 和 `loading` 插槽 ([#1180](https://github.com/arco-design/arco-design-vue/pull/1180))


## 2.25.1

`2022-04-27`

### 🐛 问题修复

- 修复 size 属性的设置在某些组件中失效的问题 ([#1051](https://github.com/arco-design/arco-design-vue/pull/1051))


## 2.25.0

`2022-04-22`

### 🆕 新增功能

- 增加 updateAtScroll 属性 ([#1015](https://github.com/arco-design/arco-design-vue/pull/1015))
- 添加 `global` 属性 ([#933](https://github.com/arco-design/arco-design-vue/pull/933))


## 2.14.1

`2022-01-08`

### 🐛 问题修复

- 修复按需加载样式问题 ([#526](https://github.com/arco-design/arco-design-vue/pull/526))


## 2.14.0

`2022-01-07`

### 🆕 新增功能

- 增加 `size` 属性 ([#513](https://github.com/arco-design/arco-design-vue/pull/513))

