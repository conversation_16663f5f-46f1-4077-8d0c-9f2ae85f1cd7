// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<config-provider> demo: render [basic] correctly 1`] = `
"<span class=\\"arco-radio-group-button arco-radio-group-size-medium arco-radio-group-direction-horizontal\\"><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"zh-CN\\"><span class=\\"arco-radio-button-content\\">zh-CN</span></label><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"en-US\\"><span class=\\"arco-radio-button-content\\">en-US</span></label><label class=\\"arco-radio-button arco-radio-checked\\"><input type=\\"radio\\" checked=\\"\\" class=\\"arco-radio-target\\" value=\\"es-ES\\"><span class=\\"arco-radio-button-content\\">es-ES</span></label><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"ja-JP\\"><span class=\\"arco-radio-button-content\\">ja-JP</span></label><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"id-ID\\"><span class=\\"arco-radio-button-content\\">id-ID</span></label><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"fr-FR\\"><span class=\\"arco-radio-button-content\\">fr-FR</span></label><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"pt-PT\\"><span class=\\"arco-radio-button-content\\">pt-PT</span></label><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"de-DE\\"><span class=\\"arco-radio-button-content\\">de-DE</span></label><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"ko-KR\\"><span class=\\"arco-radio-button-content\\">ko-KR</span></label><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"it-IT\\"><span class=\\"arco-radio-button-content\\">it-IT</span></label><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"th-TH\\"><span class=\\"arco-radio-button-content\\">th-TH</span></label><label class=\\"arco-radio-button\\"><input type=\\"radio\\" class=\\"arco-radio-target\\" value=\\"vi-VN\\"><span class=\\"arco-radio-button-content\\">vi-VN</span></label></span>
<div>
  <div class=\\"arco-pagination arco-pagination-size-medium\\" style=\\"margin-top: 20px; margin-bottom: 20px;\\"><span class=\\"arco-pagination-total\\">50 en total</span>
    <ul class=\\"arco-pagination-list\\"><span class=\\"arco-pagination-item arco-pagination-item-previous arco-pagination-item-disabled\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-left\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M32 8.4 16.444 23.956 32 39.513\\"></path></svg></span>
      <li class=\\"arco-pagination-item arco-pagination-item-active\\" pages=\\"5\\">1</li>
      <li class=\\"arco-pagination-item\\" pages=\\"5\\">2</li>
      <li class=\\"arco-pagination-item\\" pages=\\"5\\">3</li>
      <li class=\\"arco-pagination-item\\" pages=\\"5\\">4</li>
      <li class=\\"arco-pagination-item\\" pages=\\"5\\">5</li>
      <!----><span class=\\"arco-pagination-item arco-pagination-item-next\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-right\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"m16 39.513 15.556-15.557L16 8.4\\"></path></svg></span>
    </ul><span class=\\"arco-pagination-options\\"><span class=\\"arco-select-view-single arco-select arco-select-view arco-select-view-size-medium\\" title=\\"10 / página\\"><!----><input class=\\"arco-select-view-input arco-select-view-input-hidden\\" readonly=\\"\\" value=\\"\\"><span class=\\"arco-select-view-value\\">10 / página</span><span class=\\"arco-select-view-suffix\\"><!----><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down arco-select-view-arrow-icon\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></span>
    <!----></span></span>
    <!----></span><span class=\\"arco-pagination-jumper\\"><span class=\\"arco-pagination-jumper-prepend arco-pagination-jumper-text-goto\\">Ir a</span><span class=\\"arco-input-wrapper arco-input-number arco-input-number-mode-embed arco-input-number-size-medium arco-pagination-jumper-input\\"><!----><input class=\\"arco-input arco-input-size-medium\\" type=\\"text\\" role=\\"spinbutton\\" aria-valuemax=\\"5\\" aria-valuemin=\\"1\\" aria-valuenow=\\"\\" value=\\"\\"><!----><!----></span>
    <!--v-if-->
    <!--v-if--></span>
  </div>
</div>
<div class=\\"arco-space arco-space-horizontal arco-space-align-center\\" style=\\"margin-bottom: 20px;\\">
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-right: 20px;\\">
    <div class=\\"arco-picker arco-picker-range arco-picker-size-medium\\" style=\\"width: 300px;\\" visible=\\"false\\">
      <!--v-if-->
      <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"Fecha inicial\\"></div><span class=\\"arco-picker-separator\\">-</span>
      <div class=\\"arco-picker-input\\"><input placeholder=\\"Fecha final\\"></div>
      <div class=\\"arco-picker-suffix\\">
        <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-calendar\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M7 22h34M14 5v8m20-8v8M8 41h32a1 1 0 0 0 1-1V10a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v30a1 1 0 0 0 1 1Z\\"></path></svg></span>
        <!--v-if-->
      </div>
    </div>
    <!---->
  </div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-right: 20px;\\">
    <div class=\\"arco-picker arco-picker-range arco-picker-size-medium\\" style=\\"width: 300px;\\" visible=\\"false\\" editable=\\"true\\">
      <!--v-if-->
      <div class=\\"arco-picker-input arco-picker-input-active\\"><input placeholder=\\"Seleccionar hora\\"></div><span class=\\"arco-picker-separator\\"> - </span>
      <div class=\\"arco-picker-input\\"><input placeholder=\\"Seleccionar hora\\"></div>
      <div class=\\"arco-picker-suffix\\">
        <!--v-if--><span class=\\"arco-picker-suffix-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-clock-circle\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M24 14v10h9.5m8.5 0c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path></svg></span>
        <!--v-if-->
      </div>
    </div>
    <!---->
  </div>
  <!---->
  <div class=\\"arco-space-item\\"><button class=\\"arco-btn arco-btn-primary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
      <!--v-if-->Popconfirm
    </button>
    <!---->
  </div>
</div>"
`;

exports[`<config-provider> demo: render [empty] correctly 1`] = `
"<div class=\\"arco-space arco-space-vertical arco-space-fill\\">
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 8px;\\"><span class=\\"arco-select-view-single arco-select-view arco-select-view-size-medium arco-select-view-search\\" title=\\"\\"><!----><input class=\\"arco-select-view-input\\" placeholder=\\"cascader\\" value=\\"\\"><span class=\\"arco-select-view-value arco-select-view-value-hidden\\"><!----></span><span class=\\"arco-select-view-suffix\\"><!----><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down arco-select-view-arrow-icon\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></span>
    <!----></span></span>
    <!---->
  </div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 8px;\\"><span class=\\"arco-select-view-single arco-select arco-select-view arco-select-view-size-medium arco-select-view-search\\" title=\\"\\"><!----><input class=\\"arco-select-view-input\\" placeholder=\\"select\\" value=\\"\\"><span class=\\"arco-select-view-value arco-select-view-value-hidden\\"><!----></span><span class=\\"arco-select-view-suffix\\"><!----><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down arco-select-view-arrow-icon\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></span>
    <!----></span></span>
    <!---->
  </div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 8px;\\"><span class=\\"arco-select-view-single arco-select-view arco-select-view-size-medium\\" title=\\"\\"><!----><input class=\\"arco-select-view-input\\" readonly=\\"\\" placeholder=\\"tree-select\\" value=\\"\\"><span class=\\"arco-select-view-value arco-select-view-value-hidden\\"><!----></span><span class=\\"arco-select-view-suffix\\"><!----><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down arco-select-view-arrow-icon\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></span>
    <!----></span></span>
    <!---->
  </div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 8px;\\">
    <div class=\\"arco-list-wrapper\\">
      <div class=\\"arco-spin arco-list-spin\\">
        <div class=\\"arco-scrollbar arco-scrollbar-type-embed\\">
          <div class=\\"arco-scrollbar-container arco-list arco-list-medium arco-list-bordered arco-list-split\\">
            <div class=\\"arco-list-content-wrapper\\">
              <div class=\\"arco-list-header\\"> Empty List </div>
              <div role=\\"list\\" class=\\"arco-list-content\\">
                <div class=\\"arco-empty\\">
                  <div class=\\"arco-empty-image\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-empty\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
                      <path d=\\"M24 5v6m7 1 4-4m-18 4-4-4m28.5 22H28s-1 3-4 3-4-3-4-3H6.5M40 41H8a2 2 0 0 1-2-2v-8.46a2 2 0 0 1 .272-1.007l6.15-10.54A2 2 0 0 1 14.148 18H33.85a2 2 0 0 1 1.728.992l6.149 10.541A2 2 0 0 1 42 30.541V39a2 2 0 0 1-2 2Z\\"></path>
                    </svg></div>
                  <div class=\\"arco-empty-description\\">list no data!</div>
                </div>
                <!---->
              </div>
              <!---->
            </div>
          </div>
          <!--v-if-->
          <!--v-if-->
        </div>
        <!---->
        <!---->
      </div>
    </div>
  </div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 8px;\\">
    <div class=\\"arco-table arco-table-size-large arco-table-border arco-table-hover arco-table-empty\\">
      <!---->
      <div class=\\"arco-spin\\">
        <!---->
        <div class=\\"arco-table-container\\">
          <div class=\\"arco-scrollbar arco-scrollbar-type-embed\\" style=\\"height: 100%;\\">
            <div class=\\"arco-scrollbar-container arco-table-content arco-table-content-scroll-x\\">
              <table class=\\"arco-table-element\\" cellpadding=\\"0\\" cellspacing=\\"0\\">
                <colgroup>
                  <col>
                  <col>
                  <col>
                  <col>
                </colgroup>
                <thead>
                  <tr class=\\"arco-table-tr\\">
                    <th class=\\"arco-table-th\\"><span class=\\"arco-table-cell arco-table-cell-align-left\\"><span class=\\"arco-table-th-title\\">Name</span>
                      <!---->
                      <!----></span>
                      <!---->
                      <!---->
                    </th>
                    <th class=\\"arco-table-th\\"><span class=\\"arco-table-cell arco-table-cell-align-left\\"><span class=\\"arco-table-th-title\\">Salary</span>
                      <!---->
                      <!----></span>
                      <!---->
                      <!---->
                    </th>
                    <th class=\\"arco-table-th\\"><span class=\\"arco-table-cell arco-table-cell-align-left\\"><span class=\\"arco-table-th-title\\">Address</span>
                      <!---->
                      <!----></span>
                      <!---->
                      <!---->
                    </th>
                    <th class=\\"arco-table-th\\"><span class=\\"arco-table-cell arco-table-cell-align-left\\"><span class=\\"arco-table-th-title\\">Email</span>
                      <!---->
                      <!----></span>
                      <!---->
                      <!---->
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr class=\\"arco-table-tr arco-table-tr-empty\\">
                    <td class=\\"arco-table-td\\" colspan=\\"4\\"><span class=\\"arco-table-cell arco-table-cell-align-left\\"><!----><!----><span class=\\"arco-table-td-content\\"><div class=\\"arco-empty\\"><div class=\\"arco-empty-image\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-empty\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M24 5v6m7 1 4-4m-18 4-4-4m28.5 22H28s-1 3-4 3-4-3-4-3H6.5M40 41H8a2 2 0 0 1-2-2v-8.46a2 2 0 0 1 .272-1.007l6.15-10.54A2 2 0 0 1 14.148 18H33.85a2 2 0 0 1 1.728.992l6.149 10.541A2 2 0 0 1 42 30.541V39a2 2 0 0 1-2 2Z\\"></path></svg></div><div class=\\"arco-empty-description\\">table no data!</div></div></span></span></td>
                  </tr>
                </tbody>
                <!---->
              </table>
            </div>
            <!--v-if-->
            <!--v-if-->
          </div>
        </div>
        <!---->
        <!---->
        <!---->
      </div>
    </div>
  </div>
  <!---->
  <div class=\\"arco-space-item\\">
    <div class=\\"my-empty\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-trophy\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
        <path d=\\"M24 33c-6.075 0-11-4.925-11-11m11 11c6.075 0 11-4.925 11-11M24 33v8M13 22V7h22v15m-22 0V9H7v7a6 6 0 0 0 6 6Zm22 0V9h6v7a6 6 0 0 1-6 6ZM12 41h24\\"></path>
      </svg></div>
  </div>
</div>"
`;

exports[`<config-provider> demo: render [rtl] correctly 1`] = `
"<div><button type=\\"button\\" role=\\"switch\\" aria-checked=\\"true\\" class=\\"arco-switch arco-switch-type-circle arco-switch-checked\\" style=\\"margin-bottom: 20px;\\"><span class=\\"arco-switch-handle\\"><span class=\\"arco-switch-handle-icon\\"></span></span><!--  prettier-ignore  --><span class=\\"arco-switch-text-holder\\"> RTL </span><span class=\\"arco-switch-text\\"> RTL </span></button>
  <div class=\\"arco-tabs arco-tabs-horizontal arco-tabs-top arco-tabs-type-line arco-tabs-size-medium arco-tabs-rtl\\" style=\\"margin-bottom: 20px;\\">
    <!---->
    <div class=\\"arco-tabs-nav arco-tabs-nav-horizontal arco-tabs-nav-top arco-tabs-nav-size-medium arco-tabs-nav-type-line\\">
      <!---->
      <div class=\\"arco-tabs-nav-tab\\">
        <div class=\\"arco-tabs-nav-tab-list\\" style=\\"transform: translateX(0px);\\">
          <!---->
        </div>
        <!---->
      </div>
      <!---->
      <div class=\\"arco-tabs-nav-extra\\">
        <!---->
        <!---->
      </div>
    </div>
    <div class=\\"arco-tabs-content\\">
      <div class=\\"arco-tabs-content-list\\" style=\\"margin-right: -0%;\\">
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 1</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 2</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 3</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 4</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 5</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 6</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 7</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 8</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 9</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 10</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 11</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 12</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 13</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 14</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 15</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 16</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 17</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 18</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 19</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 20</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 21</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 22</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 23</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 24</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 25</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 26</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 27</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 28</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 29</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 30</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 31</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 32</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 33</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 34</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 35</div>
        </div>
        <div class=\\"arco-tabs-content-item\\">
          <div class=\\"arco-tabs-pane\\"> Content of Tab Panel 36</div>
        </div>
      </div>
    </div>
  </div>
  <div class=\\"arco-space arco-space-vertical arco-space-rtl\\" style=\\"width: 100%;\\">
    <!---->
    <div class=\\"arco-space-item\\">
      <div class=\\"arco-space arco-space-horizontal arco-space-align-center arco-space-rtl\\">
        <!---->
        <div class=\\"arco-space-item\\" style=\\"margin-left: 40px;\\"><span class=\\"arco-badge arco-badge-rtl\\"><div class=\\"arco-avatar arco-avatar-square arco-avatar-rtl\\"><span class=\\"arco-avatar-text\\"></span>
          <!--v-if-->
        </div><span class=\\"arco-badge-number\\"><span>9</span></span></span>
      </div>
      <!---->
      <div class=\\"arco-space-item\\" style=\\"margin-left: 40px;\\"><span class=\\"arco-badge arco-badge-rtl\\"><div class=\\"arco-avatar arco-avatar-square arco-avatar-rtl\\"><span class=\\"arco-avatar-text\\"></span>
        <!--v-if-->
      </div><span class=\\"arco-badge-dot\\" style=\\"width: 10px; height: 10px;\\"></span></span>
    </div>
    <!---->
    <div class=\\"arco-space-item\\" style=\\"margin-left: 40px;\\"><span class=\\"arco-badge arco-badge-rtl\\"><div class=\\"arco-avatar arco-avatar-square arco-avatar-rtl\\"><span class=\\"arco-avatar-text\\"></span>
      <!--v-if-->
    </div><span class=\\"arco-badge-custom-dot\\" style=\\"height: 16px; width: 16px; font-size: 14px;\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-clock-circle\\" style=\\"vertical-align: middle;\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M24 14v10h9.5m8.5 0c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z\\"></path></svg></span></span>
  </div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-left: 40px;\\"><span class=\\"arco-tag arco-tag-size-medium arco-tag-red arco-tag-checked arco-tag-rtl\\"><!--v-if-->red<span class=\\"arco-icon-hover arco-tag-icon-hover arco-tag-close-btn\\" role=\\"button\\" aria-label=\\"Close\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span>
    <!--v-if--></span>
  </div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-left: 40px;\\"><span class=\\"arco-tag arco-tag-size-medium arco-tag-blue arco-tag-checked arco-tag-rtl\\"><!--v-if-->blue<span class=\\"arco-icon-hover arco-tag-icon-hover arco-tag-close-btn\\" role=\\"button\\" aria-label=\\"Close\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span>
    <!--v-if--></span>
  </div>
  <!---->
  <div class=\\"arco-space-item\\"><span class=\\"arco-tag arco-tag-size-medium arco-tag-green arco-tag-checked arco-tag-rtl\\"><!--v-if-->green<span class=\\"arco-icon-hover arco-tag-icon-hover arco-tag-close-btn\\" role=\\"button\\" aria-label=\\"Close\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span>
    <!--v-if--></span>
  </div>
</div>
</div>
</div>
</div>"
`;
