```yaml
changelog: true
```

## 2.48.0

`2023-06-30`

### 🆕 Feature

- Add `exchangeTime` property ([#2529](https://github.com/arco-design/arco-design-vue/pull/2529))


## 2.47.0

`2023-06-02`

### 🆕 Feature

- empty slots add component attribute ([#2448](https://github.com/arco-design/arco-design-vue/pull/2448))


## 2.28.0

`2022-05-20`

### 🆕 Feature

- Added `empty` and `loading` slots ([#1180](https://github.com/arco-design/arco-design-vue/pull/1180))


## 2.25.1

`2022-04-27`

### 🐛 BugFix

- Fix the problem that the setting of the size attribute is invalid in some components ([#1051](https://github.com/arco-design/arco-design-vue/pull/1051))


## 2.25.0

`2022-04-22`

### 🆕 Feature

- Add updateAtScroll property ([#1015](https://github.com/arco-design/arco-design-vue/pull/1015))
- add property `global` ([#933](https://github.com/arco-design/arco-design-vue/pull/933))


## 2.14.1

`2022-01-08`

### 🐛 BugFix

- Fix the problem of loading styles on demand ([#526](https://github.com/arco-design/arco-design-vue/pull/526))


## 2.14.0

`2022-01-07`

### 🆕 Feature

- Add `size` prop ([#513](https://github.com/arco-design/arco-design-vue/pull/513))

