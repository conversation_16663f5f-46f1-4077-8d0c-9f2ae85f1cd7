// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<cascader> demo: render [basic] correctly 1`] = `
"<div class=\\"arco-space arco-space-vertical\\">
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 24px;\\"><span class=\\"arco-select-view-single arco-select-view arco-select-view-size-medium\\" style=\\"width: 320px;\\" title=\\"\\"><!----><input class=\\"arco-select-view-input\\" readonly=\\"\\" placeholder=\\"Please select ...\\" value=\\"\\"><span class=\\"arco-select-view-value arco-select-view-value-hidden\\"><!----></span><span class=\\"arco-select-view-suffix\\"><!----><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down arco-select-view-arrow-icon\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></span>
    <!----></span></span>
    <!---->
  </div>
  <!---->
  <div class=\\"arco-space-item\\"><span class=\\"arco-select-view-single arco-select-view arco-select-view-size-medium\\" style=\\"width: 320px;\\" title=\\"Beijing / ChaoYang / Datunli\\"><!----><input class=\\"arco-select-view-input arco-select-view-input-hidden\\" readonly=\\"\\" placeholder=\\"Please select ...\\" value=\\"\\"><span class=\\"arco-select-view-value\\">Beijing / ChaoYang / Datunli</span><span class=\\"arco-select-view-suffix\\"><!----><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down arco-select-view-arrow-icon\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></span>
    <!----></span></span>
    <!---->
  </div>
</div>"
`;

exports[`<cascader> demo: render [check-strictly] correctly 1`] = `
"<div class=\\"arco-space arco-space-vertical\\">
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 24px;\\"><span class=\\"arco-select-view-single arco-select-view arco-select-view-size-medium\\" style=\\"width: 320px;\\" title=\\"Beijing\\"><!----><input class=\\"arco-select-view-input arco-select-view-input-hidden\\" readonly=\\"\\" placeholder=\\"Please select ...\\" value=\\"\\"><span class=\\"arco-select-view-value\\">Beijing</span><span class=\\"arco-select-view-suffix\\"><!----><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down arco-select-view-arrow-icon\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></span>
    <!----></span></span>
    <!---->
  </div>
  <!---->
  <div class=\\"arco-space-item\\"><span class=\\"arco-select-view arco-select-view-size-medium arco-select-view-has-tag arco-select-view-has-suffix arco-select-view-multiple\\" style=\\"width: 320px;\\"><span class=\\"arco-select-view-mirror\\"></span>
    <!---->
    <transition-group-stub tag=\\"span\\" name=\\"input-tag-zoom\\" appear=\\"false\\" persisted=\\"false\\" css=\\"true\\" class=\\"arco-select-view-inner\\"><span class=\\"arco-tag arco-tag-size-medium arco-tag-checked arco-select-view-tag\\"><!--v-if-->Beijing<span class=\\"arco-icon-hover arco-tag-icon-hover arco-tag-close-btn\\" role=\\"button\\" aria-label=\\"Close\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span>
      <!--v-if--></span><input class=\\"arco-select-view-input\\" style=\\"width: 12px;\\">
    </transition-group-stub>
    <!----><span class=\\"arco-select-view-suffix\\"><!----><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down arco-select-view-arrow-icon\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></span>
    <!---->
    <!----></span></span>
    <!---->
  </div>
</div>"
`;

exports[`<cascader> demo: render [clear] correctly 1`] = `
"<span class=\\"arco-select-view-single arco-select-view arco-select-view-size-medium\\" style=\\"width: 320px;\\" title=\\"Beijing / ChaoYang / Datunli\\"><!----><input class=\\"arco-select-view-input arco-select-view-input-hidden\\" readonly=\\"\\" placeholder=\\"Please select ...\\" value=\\"\\"><span class=\\"arco-select-view-value\\">Beijing / ChaoYang / Datunli</span><span class=\\"arco-select-view-suffix\\"><span class=\\"arco-icon-hover arco-select-view-clear-btn\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down arco-select-view-arrow-icon\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></span>
<!----></span></span>"
`;

exports[`<cascader> demo: render [disabled] correctly 1`] = `
"<span class=\\"arco-select-view-single arco-select-view arco-select-view-size-medium\\" style=\\"width: 320px;\\" title=\\"\\"><!----><input class=\\"arco-select-view-input\\" readonly=\\"\\" placeholder=\\"Please select ...\\" value=\\"\\"><span class=\\"arco-select-view-value arco-select-view-value-hidden\\"><!----></span><span class=\\"arco-select-view-suffix\\"><!----><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down arco-select-view-arrow-icon\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></span>
<!----></span></span>"
`;

exports[`<cascader> demo: render [expand] correctly 1`] = `
"<span class=\\"arco-select-view-single arco-select-view arco-select-view-size-medium\\" style=\\"width: 320px;\\" title=\\"\\"><!----><input class=\\"arco-select-view-input\\" readonly=\\"\\" placeholder=\\"Please select ...\\" value=\\"\\"><span class=\\"arco-select-view-value arco-select-view-value-hidden\\"><!----></span><span class=\\"arco-select-view-suffix\\"><!----><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down arco-select-view-arrow-icon\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></span>
<!----></span></span>"
`;

exports[`<cascader> demo: render [fallback] correctly 1`] = `
"<div class=\\"arco-space arco-space-vertical\\">
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 24px;\\"><span class=\\"arco-select-view arco-select-view-size-medium arco-select-view-has-tag arco-select-view-has-suffix arco-select-view-multiple\\" style=\\"width: 320px;\\"><span class=\\"arco-select-view-mirror\\"></span>
    <!---->
    <transition-group-stub tag=\\"span\\" name=\\"input-tag-zoom\\" appear=\\"false\\" persisted=\\"false\\" css=\\"true\\" class=\\"arco-select-view-inner\\"><span class=\\"arco-tag arco-tag-size-medium arco-tag-checked arco-select-view-tag\\"><!--v-if-->Beijing / ChaoYang / Datunli<span class=\\"arco-icon-hover arco-tag-icon-hover arco-tag-close-btn\\" role=\\"button\\" aria-label=\\"Close\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span>
      <!--v-if--></span><span class=\\"arco-tag arco-tag-size-medium arco-tag-checked arco-select-view-tag\\"><!--v-if-->wuhou<span class=\\"arco-icon-hover arco-tag-icon-hover arco-tag-close-btn\\" role=\\"button\\" aria-label=\\"Close\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span>
      <!--v-if--></span><input class=\\"arco-select-view-input\\" style=\\"width: 12px;\\">
    </transition-group-stub>
    <!----><span class=\\"arco-select-view-suffix\\"><!----><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down arco-select-view-arrow-icon\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></span>
    <!---->
    <!----></span></span>
    <!---->
  </div>
  <!---->
  <div class=\\"arco-space-item\\"><span class=\\"arco-select-view arco-select-view-size-medium arco-select-view-has-tag arco-select-view-has-suffix arco-select-view-multiple\\" style=\\"width: 320px;\\"><span class=\\"arco-select-view-mirror\\"></span>
    <!---->
    <transition-group-stub tag=\\"span\\" name=\\"input-tag-zoom\\" appear=\\"false\\" persisted=\\"false\\" css=\\"true\\" class=\\"arco-select-view-inner\\"><span class=\\"arco-tag arco-tag-size-medium arco-tag-checked arco-select-view-tag\\"><!--v-if-->Beijing / ChaoYang / Datunli<span class=\\"arco-icon-hover arco-tag-icon-hover arco-tag-close-btn\\" role=\\"button\\" aria-label=\\"Close\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span>
      <!--v-if--></span><span class=\\"arco-tag arco-tag-size-medium arco-tag-checked arco-select-view-tag\\"><!--v-if-->SICHUAN-CHENGDU-WUHOU<span class=\\"arco-icon-hover arco-tag-icon-hover arco-tag-close-btn\\" role=\\"button\\" aria-label=\\"Close\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span>
      <!--v-if--></span><input class=\\"arco-select-view-input\\" style=\\"width: 12px;\\">
    </transition-group-stub>
    <!----><span class=\\"arco-select-view-suffix\\"><!----><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down arco-select-view-arrow-icon\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></span>
    <!---->
    <!----></span></span>
    <!---->
  </div>
</div>"
`;

exports[`<cascader> demo: render [field-names] correctly 1`] = `
"<span class=\\"arco-select-view-single arco-select-view arco-select-view-size-medium\\" style=\\"width: 320px;\\" title=\\"\\"><!----><input class=\\"arco-select-view-input\\" readonly=\\"\\" placeholder=\\"Please select ...\\" value=\\"\\"><span class=\\"arco-select-view-value arco-select-view-value-hidden\\"><!----></span><span class=\\"arco-select-view-suffix\\"><!----><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down arco-select-view-arrow-icon\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></span>
<!----></span></span>"
`;

exports[`<cascader> demo: render [format] correctly 1`] = `
"<span class=\\"arco-select-view-single arco-select-view arco-select-view-size-medium\\" style=\\"width: 320px;\\" title=\\"Beijing-ChaoYang-Datunli\\"><!----><input class=\\"arco-select-view-input arco-select-view-input-hidden\\" readonly=\\"\\" placeholder=\\"Please select ...\\" value=\\"\\"><span class=\\"arco-select-view-value\\">Beijing-ChaoYang-Datunli</span><span class=\\"arco-select-view-suffix\\"><!----><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down arco-select-view-arrow-icon\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></span>
<!----></span></span>"
`;

exports[`<cascader> demo: render [lazy-load] correctly 1`] = `
"<div class=\\"arco-space arco-space-horizontal arco-space-align-center\\">
  <!---->
  <div class=\\"arco-space-item\\"><span class=\\"arco-select-view-single arco-select-view arco-select-view-size-medium\\" style=\\"width: 320px;\\" title=\\"\\"><!----><input class=\\"arco-select-view-input\\" readonly=\\"\\" placeholder=\\"Please select ...\\" value=\\"\\"><span class=\\"arco-select-view-value arco-select-view-value-hidden\\"><!----></span><span class=\\"arco-select-view-suffix\\"><!----><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down arco-select-view-arrow-icon\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></span>
    <!----></span></span>
    <!---->
  </div>
</div>"
`;

exports[`<cascader> demo: render [loading] correctly 1`] = `
"<span class=\\"arco-select-view-single arco-select-view arco-select-view-size-medium\\" style=\\"width: 320px;\\" title=\\"\\"><!----><input class=\\"arco-select-view-input\\" readonly=\\"\\" placeholder=\\"Please select ...\\" value=\\"\\"><span class=\\"arco-select-view-value arco-select-view-value-hidden\\"><!----></span><span class=\\"arco-select-view-suffix\\"><!----><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-loading\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M42 24c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6\\"></path></svg></span>
<!----></span></span>"
`;

exports[`<cascader> demo: render [multiple] correctly 1`] = `
"<span class=\\"arco-select-view arco-select-view-size-medium arco-select-view-has-tag arco-select-view-has-suffix arco-select-view-multiple\\" style=\\"width: 320px;\\"><span class=\\"arco-select-view-mirror\\"></span>
<!---->
<transition-group-stub tag=\\"span\\" name=\\"input-tag-zoom\\" appear=\\"false\\" persisted=\\"false\\" css=\\"true\\" class=\\"arco-select-view-inner\\"><span class=\\"arco-tag arco-tag-size-medium arco-tag-checked arco-select-view-tag\\"><!--v-if-->Beijing / ChaoYang / Datunli<span class=\\"arco-icon-hover arco-tag-icon-hover arco-tag-close-btn\\" role=\\"button\\" aria-label=\\"Close\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-close\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142\\"></path></svg></span>
  <!--v-if--></span><input class=\\"arco-select-view-input\\" style=\\"width: 12px;\\">
</transition-group-stub>
<!----><span class=\\"arco-select-view-suffix\\"><!----><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down arco-select-view-arrow-icon\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></span>
<!---->
<!----></span></span>"
`;

exports[`<cascader> demo: render [panel] correctly 1`] = `
"<transition-group-stub tag=\\"div\\" name=\\"cascader-slide\\" appear=\\"false\\" persisted=\\"false\\" css=\\"true\\" class=\\"arco-cascader-panel\\">
  <div class=\\"arco-cascader-panel-column\\" style=\\"z-index: 3;\\">
    <div class=\\"arco-scrollbar arco-scrollbar-type-embed\\">
      <div class=\\"arco-scrollbar-container arco-cascader-column-content\\">
        <ul role=\\"menu\\" class=\\"arco-cascader-list\\">
          <li tabindex=\\"0\\" role=\\"menuitem\\" aria-disabled=\\"false\\" aria-haspopup=\\"true\\" aria-expanded=\\"false\\" title=\\"Beijing\\" class=\\"arco-cascader-option\\">
            <!---->
            <!---->
            <div class=\\"arco-cascader-option-label\\">Beijing<svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-right\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
                <path d=\\"m16 39.513 15.556-15.557L16 8.4\\"></path>
              </svg></div>
          </li>
          <li tabindex=\\"0\\" role=\\"menuitem\\" aria-disabled=\\"false\\" aria-haspopup=\\"true\\" aria-expanded=\\"false\\" title=\\"Shanghai\\" class=\\"arco-cascader-option\\">
            <!---->
            <!---->
            <div class=\\"arco-cascader-option-label\\">Shanghai<svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-right\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\">
                <path d=\\"m16 39.513 15.556-15.557L16 8.4\\"></path>
              </svg></div>
          </li>
        </ul>
      </div>
      <!--v-if-->
      <!--v-if-->
    </div>
  </div>
</transition-group-stub>"
`;

exports[`<cascader> demo: render [path] correctly 1`] = `
"<div class=\\"arco-space arco-space-vertical\\">
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-bottom: 24px;\\"><span class=\\"arco-select-view-single arco-select-view arco-select-view-size-medium\\" style=\\"width: 320px;\\" title=\\"\\"><!----><input class=\\"arco-select-view-input\\" readonly=\\"\\" placeholder=\\"Please select ...\\" value=\\"\\"><span class=\\"arco-select-view-value arco-select-view-value-hidden\\"><!----></span><span class=\\"arco-select-view-suffix\\"><!----><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down arco-select-view-arrow-icon\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></span>
    <!----></span></span>
    <!---->
  </div>
  <!---->
  <div class=\\"arco-space-item\\"><span class=\\"arco-select-view-single arco-select-view arco-select-view-size-medium\\" style=\\"width: 320px;\\" title=\\"Beijing / ChaoYang / Datunli\\"><!----><input class=\\"arco-select-view-input arco-select-view-input-hidden\\" readonly=\\"\\" placeholder=\\"Please select ...\\" value=\\"\\"><span class=\\"arco-select-view-value\\">Beijing / ChaoYang / Datunli</span><span class=\\"arco-select-view-suffix\\"><!----><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down arco-select-view-arrow-icon\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></span>
    <!----></span></span>
    <!---->
  </div>
</div>"
`;

exports[`<cascader> demo: render [search] correctly 1`] = `
"<span class=\\"arco-select-view-single arco-select-view arco-select-view-size-medium arco-select-view-search\\" style=\\"width: 320px;\\" title=\\"\\"><!----><input class=\\"arco-select-view-input\\" placeholder=\\"Please select ...\\" value=\\"\\"><span class=\\"arco-select-view-value arco-select-view-value-hidden\\"><!----></span><span class=\\"arco-select-view-suffix\\"><!----><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down arco-select-view-arrow-icon\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></span>
<!----></span></span>"
`;

exports[`<cascader> demo: render [virtual] correctly 1`] = `
"<span class=\\"arco-select-view-single arco-select-view arco-select-view-size-medium\\" style=\\"width: 320px;\\" title=\\"\\"><!----><input class=\\"arco-select-view-input\\" readonly=\\"\\" placeholder=\\"Please select ...\\" value=\\"\\"><span class=\\"arco-select-view-value arco-select-view-value-hidden\\"><!----></span><span class=\\"arco-select-view-suffix\\"><!----><span class=\\"arco-select-view-icon\\"><svg viewBox=\\"0 0 48 48\\" fill=\\"none\\" xmlns=\\"http://www.w3.org/2000/svg\\" stroke=\\"currentColor\\" class=\\"arco-icon arco-icon-down arco-select-view-arrow-icon\\" stroke-width=\\"4\\" stroke-linecap=\\"butt\\" stroke-linejoin=\\"miter\\"><path d=\\"M39.6 17.443 24.043 33 8.487 17.443\\"></path></svg></span>
<!----></span></span>"
`;
