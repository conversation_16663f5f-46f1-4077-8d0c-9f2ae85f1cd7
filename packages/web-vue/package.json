{"name": "@arco-design/web-vue", "version": "2.57.0", "description": "Arco Design Vue 2.0: A Vue.js 3 UI Library", "keywords": ["arco", "vue"], "author": "ArcoDesign Team", "homepage": "https://arco.design/vue", "license": "MIT", "main": "lib/index.js", "module": "es/index.js", "types": "es/index.d.ts", "unpkg": "dist/arco-vue.min.js", "files": ["es", "lib", "dist", "json"], "repository": {"type": "git", "url": "git+https://github.com/arco-design/arco-design-vue.git"}, "sideEffects": ["dist/*", "es/**/style/*", "lib/**/style/*", "*.less"], "scripts": {"init": "pnpm run icongen && pnpm run lessgen && arco-vue-scripts build:component", "start": "arco-vue-scripts dev:component", "docgen": "arco-vue-scripts docgen", "icongen": "arco-vue-scripts icongen && pnpm run dtsgen:icon", "lessgen": "arco-vue-scripts lessgen", "jsongen": "arco-vue-scripts jsongen", "build": "pnpm run build:component && pnpm run build:style && pnpm run dtsgen && pnpm run jsongen", "build:component": "arco-vue-scripts build:component -u", "build:module": "arco-vue-scripts build:component", "build:style": "arco-vue-scripts build:style", "dtsgen": "arco-vue-scripts dtsgen 'components/**/*.{ts,tsx,vue}'", "dtsgen:icon": "arco-vue-scripts dtsgen 'components/icon/**/*.{ts,tsx,vue}' -o 'components/icon/@types'", "test": "arco-vue-scripts test --coverage", "test:update": "arco-vue-scripts test --coverage -u", "test:screenshot": "arco-vue-scripts test:screenshot -o ./__screenshots__/", "changelog": "arco-vue-scripts changelog", "lint-staged": "npx lint-staged"}, "peerDependencies": {"vue": ">=3.1.0"}, "lint-staged": {"*.{js,ts,jsx,tsx,vue}": ["eslint --fix", "prettier --write"], "*.{less,css}": ["stylelint --fix", "prettier --write"]}, "devDependencies": {"@arco-design/arco-vue-scripts": "workspace:*", "@babel/core": "^7.26.9", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-runtime": "^7.26.9", "@babel/preset-env": "^7.26.9", "@babel/preset-typescript": "^7.26.0", "@types/jest": "^26.0.24", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vue/babel-plugin-jsx": "^1.4.0", "@vue/test-utils": "^2.4.6", "eslint": "^7.32.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-config-prettier": "^8.10.0", "eslint-import-resolver-typescript": "^2.7.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^7.20.0", "jest": "^26.6.3", "prettier": "^2.8.8", "stylelint": "^13.13.1", "stylelint-config-prettier": "^8.0.2", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-standard": "^20.0.0", "stylelint-order": "^4.1.0", "typescript": "^4.9.5", "vue-jest": "5.0.0-alpha.10"}, "dependencies": {"@arco-design/color": "^0.4.0", "b-tween": "^0.3.3", "b-validate": "^1.5.3", "compute-scroll-into-view": "^1.0.20", "dayjs": "^1.11.13", "number-precision": "^1.6.0", "resize-observer-polyfill": "^1.5.1", "scroll-into-view-if-needed": "^2.2.31", "vue": "^3.1.0"}, "vetur": {"tags": "json/vetur-tags.json", "attributes": "json/vetur-attributes.json"}, "web-types": "json/web-types.json"}