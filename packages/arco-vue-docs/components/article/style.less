@import '../../../web-vue/components/style/theme/css-variables.less';

.arco-vue-main {
  margin-left: 13px;
  transition: margin-left 200ms, margin-right 200ms;

  &.aside-nav-show {
    margin-left: 261px;
  }

  &.aside-anchor-show {
    margin-right: 181px;
    border-right-color: var(--color-bg-1);
  }
}

.arco-vue-article {
  max-width: 985px;
  min-height: 800px;
  margin: 0 auto;
  padding: 0 56px 100px;

  .article-header {
    position: relative;
    padding: 36px 0;
    border-bottom: 1px solid var(--color-border);
  }

  .article-meta {
    .separator {
      padding: 0 5px;
      color: var(--color-text-4);
    }

    .article-category {
      color: var(--color-text-1);
      font-weight: 500;
    }
  }

  .article-title {
    margin: 20px 0 12px;
    color: var(--color-text-1);
    font-weight: 500;
    font-size: @font-size-display-1;
  }

  .article-description {
    color: var(--color-text-2);
    line-height: 20px;

    &:not(:last-child) {
      margin-right: 200px;
    }
  }

  .article-content {
    &:deep(img) {
      max-width: 100%;
    }
  }

  //& :deep(a) {
  //  color: rgb(var(--arcoblue-6));
  //  text-decoration: none;
  //}
  & :deep(h3) {
    margin-top: 24px;
    line-height: 24px;
  }

  & :deep(code) {
    padding: 2px 8px;
    color: var(--color-text-2);
    background-color: var(--color-neutral-2);
  }

  & :deep(.code-content) {
    box-sizing: border-box;
    margin: 0;
    padding: 28px 48px;
    background-color: var(--color-neutral-2);
    border-radius: 4px;

    code {
      padding: 0;
      white-space: pre-wrap;
    }
  }

  & :deep(.component-api-table) {
    .arco-table-td:nth-child(1) {
      white-space: nowrap;
    }

    .arco-table-td:nth-child(3) {
      code {
        color: rgb(var(--primary-6));
        font-size: 12px;
        background: unset;
      }
    }

    .arco-table-td:nth-child(4) {
      min-width: 100px;

      code {
        color: rgb(var(--primary-6));
        font-size: 12px;
        background: unset;
      }
    }

    .arco-table-td:nth-child(5) {
      width: 10%;
      max-width: 100px;
    }
  }
}
