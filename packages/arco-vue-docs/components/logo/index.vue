<template>
  <svg width="120px" height="24px" viewBox="0 0 120 24" version="1.1">
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
      <g id="画板" transform="translate(-56.000000, -14.000000)">
        <g id="编组-6备份" transform="translate(26.000000, 14.000000)">
          <g id="编组-3">
            <g
              id="编组-2"
              transform="translate(76.300000, 15.000000)"
              fill="var(--color-text-1)"
            >
              <path
                id="椭圆形备份"
                d="M2.34361572,4.26825799 C3.36534251,4.26825799 4.19361572,3.43998478 4.19361572,2.41825799 C4.19361572,1.39653121 3.36534251,0.568257993 2.34361572,0.568257993 C1.32188894,0.568257993 0.493615723,1.39653121 0.493615723,2.41825799 C0.493615723,3.43998478 1.32188894,4.26825799 2.34361572,4.26825799 Z"
              />
            </g>
          </g>
          <path
            id="arco.design"
            d="M45.4540018,8.33500087 L45.4540018,18.9940026 L42.6490014,18.9940026 L42.6490014,17.8170024 C41.9560013,18.6750026 40.9220011,19.2690027 39.4590008,19.2690027 C36.5220004,19.2690027 34.3,16.8600023 34.3,13.6590017 C34.3,10.4580012 36.5220004,8.04900083 39.4590008,8.04900083 C40.9220011,8.04900083 41.9450013,8.64300092 42.6490014,9.51200107 L42.6490014,8.33500087 L45.4540018,8.33500087 Z M39.9870009,10.7220013 C38.3040007,10.7220013 37.2370005,12.0310015 37.2370005,13.6590017 C37.2370005,15.287002 38.3040007,16.5960022 39.9870009,16.5960022 C41.6260012,16.5960022 42.7370005,15.342002 42.7370005,13.6590017 C42.7370005,11.9760015 41.6260012,10.7220013 39.9870009,10.7220013 Z M53.1540011,8.04900083 C53.7700012,8.04900083 54.2540012,8.13700084 54.6500013,8.28000087 L54.2210012,11.1290013 C53.7480012,10.9200013 53.066001,10.7880013 52.4830009,10.7880013 C51.1520007,10.7880013 50.2390006,11.6020014 50.2390006,13.1860017 L50.2390006,18.9940026 L47.3680001,18.9940026 L47.3680001,8.33500087 L50.1510006,8.33500087 L50.1510006,9.50100107 C50.8220007,8.5000009 51.8450008,8.04900083 53.1540011,8.04900083 Z M60.1610009,8.04900083 C61.9430012,8.04900083 63.4500015,8.77500095 64.4290016,10.0620012 L62.3610013,11.7890014 C61.7120012,11.0960013 61.0190011,10.7220013 60.0840009,10.7220013 C58.5990007,10.7220013 57.4110005,11.8880015 57.4110005,13.6590017 C57.4110005,15.441002 58.5880007,16.5960022 60.0730009,16.5960022 C60.9970011,16.5960022 61.7890012,16.1780022 62.3940013,15.5180021 L64.4400016,17.2670023 C63.4390015,18.5540025 61.9430012,19.2690027 60.1610009,19.2690027 C56.7620004,19.2690027 54.485,16.8930023 54.485,13.6590017 C54.485,10.4360012 56.7620004,8.04900083 60.1610009,8.04900083 Z M70.2810009,8.04900083 C73.6030015,8.04900083 76.0560019,10.4470012 76.0560019,13.6590017 C76.0560019,16.8710023 73.6030015,19.2690027 70.2810009,19.2690027 C66.9700004,19.2690027 64.528,16.8710023 64.528,13.6590017 C64.528,10.4470012 66.9700004,8.04900083 70.2810009,8.04900083 Z M70.2810009,10.7220013 C68.6640007,10.7220013 67.4540005,11.9320015 67.4540005,13.6590017 C67.4540005,15.386002 68.6640007,16.5960022 70.2810009,16.5960022 C71.8980012,16.5960022 73.1190014,15.386002 73.1190014,13.6590017 C73.1190014,11.9320015 71.8980012,10.7220013 70.2810009,10.7220013 Z M86.5280008,8.04900083 C87.9580011,8.04900083 88.9700012,8.61000092 89.6630014,9.44600106 L89.6630014,3.15400003 L92.5230018,3.15400003 L92.5230018,18.9940026 L89.7180014,18.9940026 L89.7180014,17.8170024 C89.0250013,18.6750026 87.9910011,19.2690027 86.5280008,19.2690027 C83.5910004,19.2690027 81.369,16.8600023 81.369,13.6590017 C81.369,10.4580012 83.5910004,8.04900083 86.5280008,8.04900083 Z M87.0560009,10.7220013 C85.3730007,10.7220013 84.3060005,12.0310015 84.3060005,13.6590017 C84.3060005,15.287002 85.3730007,16.5960022 87.0560009,16.5960022 C88.6950012,16.5960022 89.8060005,15.342002 89.8060005,13.6590017 C89.8060005,11.9760015 88.6950012,10.7220013 87.0560009,10.7220013 Z M99.1670009,8.04900083 C102.236001,8.04900083 104.337002,10.3260012 104.348002,13.6480017 C104.348002,13.9780018 104.326002,14.3740019 104.293002,14.6160019 L96.7360005,14.6160019 C97.0440005,16.1560022 98.1220007,16.8270023 99.4750009,16.8270023 C100.399001,16.8270023 101.400001,16.4530022 102.170001,15.8040021 L103.853002,17.6520024 C102.643001,18.7630026 101.103001,19.2690027 99.3100009,19.2690027 C96.0430004,19.2690027 93.777,17.0470023 93.777,13.6920018 C93.777,10.3370012 95.9770004,8.04900083 99.1670009,8.04900083 Z M99.1450009,10.5240012 C97.7920007,10.5240012 97.0000005,11.3380014 96.7470005,12.6580016 L101.466001,12.6580016 C101.202001,11.2940014 100.377001,10.5240012 99.1450009,10.5240012 Z M114.127002,9.072001 L113.159001,11.3050014 C112.312001,10.8870013 110.959001,10.4470012 109.760001,10.4360012 C108.671001,10.4360012 108.121001,10.8210013 108.121001,11.4040014 C108.121001,12.0200015 108.891001,12.1740015 109.859001,12.3170015 L110.805001,12.4600016 C113.115001,12.8120016 114.402002,13.8460018 114.402002,15.6500021 C114.402002,17.8500024 112.598001,19.2690027 109.507001,19.2690027 C108.055001,19.2690027 106.152,18.9940026 104.766,18.0150025 L105.899,15.8370021 C106.812,16.4420022 107.923001,16.8820023 109.529001,16.8820023 C110.860001,16.8820023 111.498001,16.5080022 111.498001,15.8810021 C111.498001,15.364002 110.959001,15.078002 109.727001,14.902002 L108.858001,14.7810019 C106.394,14.4400019 105.173,13.3620017 105.173,11.5470014 C105.173,9.35800104 106.867,8.06000083 109.672001,8.06000083 C111.377001,8.06000083 112.708001,8.37900088 114.127002,9.072001 Z M118.439001,8.33500087 L118.439001,18.9940026 L115.568,18.9940026 L115.568,8.33500087 L118.439001,8.33500087 Z M116.987,3.15400003 C118.021001,3.15400003 118.824001,3.96800016 118.824001,5.00200033 C118.824001,6.0360005 118.021001,6.82800063 116.987,6.82800063 C115.953,6.82800063 115.161,6.0360005 115.161,5.00200033 C115.161,3.96800016 115.953,3.15400003 116.987,3.15400003 Z M124.599001,8.04900083 C126.084001,8.04900083 127.173001,8.62100092 127.899001,9.52300107 L127.899001,8.33500087 L130.715002,8.33500087 L130.715002,18.1140025 C130.715002,21.535003 128.482001,23.6030034 124.962001,23.6030034 C123.279001,23.6030034 121.497,23.1630033 120.298,22.3160032 L121.321,19.9730028 C122.388,20.6770029 123.565001,21.062003 124.841001,21.062003 C126.623001,21.062003 127.877001,20.0720028 127.877001,18.2350025 L127.877001,17.4870024 C127.151001,18.3780025 126.073001,18.9280026 124.599001,18.9280026 C122.003,18.9280026 119.693,16.7610023 119.693,13.4940017 C119.693,10.2160012 122.003,8.04900083 124.599001,8.04900083 Z M125.325001,10.6890013 C123.686001,10.6890013 122.641,11.9430015 122.641,13.4940017 C122.641,15.034002 123.686001,16.2880022 125.325001,16.2880022 C126.920001,16.2880022 127.998001,15.078002 127.998001,13.4940017 C127.998001,11.8990015 126.920001,10.6890013 125.325001,10.6890013 Z M138.558001,8.04900083 C140.978001,8.04900083 142.705002,9.7320011 142.705002,12.2070015 L142.705002,18.9940026 L139.834001,18.9940026 L139.834001,13.1310017 C139.834001,11.5910014 139.064001,10.7440013 137.755001,10.7440013 C136.523001,10.7440013 135.500001,11.5470014 135.500001,13.1530017 L135.500001,18.9940026 L132.629,18.9940026 L132.629,8.33500087 L135.423001,8.33500087 L135.423001,9.58900108 C136.182001,8.45600089 137.348001,8.04900083 138.558001,8.04900083 Z"
            fill="var(--color-text-1)"
            fill-rule="nonzero"
          />
        </g>
      </g>
    </g>
  </svg>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'ArcoLogo',
});
</script>
