export const tokens = [
  {
    name: '主色',
    nameEN: 'Primary',
    type: 'color',
    tokens: [
      {
        name: 'primary-6',
        desc: '常规',
        descEN: 'Normal',
        value: 'rgb(var(--arcoblue-6))',
        cssvar: true,
      },
      {
        name: 'primary-5',
        desc: '悬浮（hover）',
        descEN: 'Hover',
        value: 'rgb(var(--arcoblue-5))',
        cssvar: true,
      },
      {
        name: 'primary-7',
        desc: '点击（click）',
        descEN: 'Click',
        value: 'rgb(var(--arcoblue-7))',
        cssvar: true,
      },
      {
        name: 'primary-4',
        desc: '特殊场景',
        descEN: 'Special scene',
        value: 'rgb(var(--arcoblue-4))',
        cssvar: true,
      },
      {
        name: 'primary-3',
        desc: '一般禁用',
        descEN: 'Normally disabled',
        value: 'rgb(var(--arcoblue-3))',
        cssvar: true,
      },
      {
        name: 'primary-2',
        desc: '文字禁用',
        descEN: 'Text disabled',
        value: 'rgb(var(--arcoblue-2))',
        cssvar: true,
      },
      {
        name: 'primary-1',
        desc: '浅色/白底悬浮',
        descEN: 'Light/white suspension',
        value: 'rgb(var(--arcoblue-1))',
        cssvar: true,
      },
    ],
  },
  {
    name: '成功色',
    nameEN: 'Success',
    type: 'color',
    tokens: [
      {
        name: 'success-6',
        desc: '常规',
        descEN: 'Normal',
        value: 'rgb(var(--green-6))',
        cssvar: true,
      },
      {
        name: 'success-5',
        desc: '悬浮（hover）',
        descEN: 'Hover',
        value: 'rgb(var(--green-5))',
        cssvar: true,
      },
      {
        name: 'success-7',
        desc: '点击（click）',
        descEN: 'Click',
        value: 'rgb(var(--green-7))',
        cssvar: true,
      },
      {
        name: 'success-4',
        desc: '特殊场景',
        descEN: 'Special scene',
        value: 'rgb(var(--green-4))',
        cssvar: true,
      },
      {
        name: 'success-3',
        desc: '一般禁用',
        descEN: 'Normally disabled',
        value: 'rgb(var(--green-3))',
        cssvar: true,
      },
      {
        name: 'success-2',
        desc: '文字禁用',
        descEN: 'Text disabled',
        value: 'rgb(var(--green-2))',
        cssvar: true,
      },
      {
        name: 'success-1',
        desc: '浅色/白底悬浮',
        descEN: 'Light/white suspension',
        value: 'rgb(var(--green-1))',
        cssvar: true,
      },
    ],
  },
  {
    name: '警示色',
    nameEN: 'Warning',
    type: 'color',
    tokens: [
      {
        name: 'warning-6',
        desc: '常规',
        descEN: 'Normal',
        value: 'rgb(var(--orange-6))',
        cssvar: true,
      },
      {
        name: 'warning-5',
        desc: '悬浮（hover）',
        descEN: 'Hover',
        value: 'rgb(var(--orange-5))',
        cssvar: true,
      },
      {
        name: 'warning-7',
        desc: '点击（click）',
        descEN: 'Click',
        value: 'rgb(var(--orange-7))',
        cssvar: true,
      },
      {
        name: 'warning-4',
        desc: '特殊场景',
        descEN: 'Special scene',
        value: 'rgb(var(--orange-4))',
        cssvar: true,
      },
      {
        name: 'warning-3',
        desc: '一般禁用',
        descEN: 'Normally disabled',
        value: 'rgb(var(--orange-3))',
        cssvar: true,
      },
      {
        name: 'warning-2',
        desc: '文字禁用',
        descEN: 'Text disabled',
        value: 'rgb(var(--orange-2))',
        cssvar: true,
      },
      {
        name: 'warning-1',
        desc: '浅色/白底悬浮',
        descEN: 'Light/white suspension',
        value: 'rgb(var(--orange-1))',
        cssvar: true,
      },
    ],
  },
  {
    name: '错误色',
    nameEN: 'Danger',
    type: 'color',
    tokens: [
      {
        name: 'danger-6',
        desc: '常规',
        descEN: 'Normal',
        value: 'rgb(var(--red-6))',
        cssvar: true,
      },
      {
        name: 'danger-5',
        desc: '悬浮（hover）',
        descEN: 'Hover',
        value: 'rgb(var(--red-5))',
        cssvar: true,
      },
      {
        name: 'danger-7',
        desc: '点击（click）',
        descEN: 'Click',
        value: 'rgb(var(--red-7))',
        cssvar: true,
      },
      {
        name: 'danger-4',
        desc: '特殊场景',
        descEN: 'Special scene',
        value: 'rgb(var(--red-4))',
        cssvar: true,
      },
      {
        name: 'danger-3',
        desc: '一般禁用',
        descEN: 'Normally disabled',
        value: 'rgb(var(--red-3))',
        cssvar: true,
      },
      {
        name: 'danger-2',
        desc: '文字禁用',
        descEN: 'Text disabled',
        value: 'rgb(var(--red-2))',
        cssvar: true,
      },
      {
        name: 'danger-1',
        desc: '浅色/白底悬浮',
        descEN: 'Light/white suspension',
        value: 'rgb(var(--red-1))',
        cssvar: true,
      },
    ],
  },
  {
    name: '链接色',
    nameEN: 'Link',
    type: 'color',
    tokens: [
      {
        name: 'link-6',
        desc: '常规',
        descEN: 'Normal',
        value: 'rgb(var(--arcoblue-6))',
        cssvar: true,
      },
      {
        name: 'link-5',
        desc: '悬浮（hover）',
        descEN: 'Hover',
        value: 'rgb(var(--arcoblue-5))',
        cssvar: true,
      },
      {
        name: 'link-7',
        desc: '点击（click）',
        descEN: 'Click',
        value: 'rgb(var(--arcoblue-7))',
        cssvar: true,
      },
      {
        name: 'link-4',
        desc: '特殊场景',
        descEN: 'Special scene',
        value: 'rgb(var(--arcoblue-4))',
        cssvar: true,
      },
      {
        name: 'link-3',
        desc: '一般禁用',
        descEN: 'Normally disabled',
        value: 'rgb(var(--arcoblue-3))',
        cssvar: true,
      },
      {
        name: 'link-2',
        desc: '文字禁用',
        descEN: 'Text disabled',
        value: 'rgb(var(--arcoblue-2))',
        cssvar: true,
      },
      {
        name: 'link-1',
        desc: '浅色/白底悬浮',
        descEN: 'Light/white suspension',
        value: 'rgb(var(--arcoblue-1))',
        cssvar: true,
      },
    ],
  },
  {
    name: '边框颜色',
    nameEN: 'Border Color',
    type: 'color',
    tokens: [
      {
        name: 'color-border-1',
        desc: '浅色',
        descEN: 'Light color',
        value: 'var(--color-neutral-2)',
        cssvar: true,
      },
      {
        name: 'color-border-2',
        desc: '一般',
        descEN: 'Normal',
        value: 'var(--color-neutral-3)',
        cssvar: true,
      },
      {
        name: 'color-border-3',
        desc: '深/悬浮',
        descEN: 'Deep/Hover',
        value: 'var(--color-neutral-4)',
        cssvar: true,
      },
      {
        name: 'color-border-4',
        desc: '重/按钮描边',
        descEN: 'Heavy/Button Border',
        value: 'var(--color-neutral-6)',
        cssvar: true,
      },
    ],
  },
  {
    name: '填充颜色',
    nameEN: 'Fill Color',
    type: 'color',
    tokens: [
      {
        name: 'color-fill-1',
        desc: '浅/禁用',
        descEN: 'Light/Disabled',
        value: 'var(--color-neutral-1)',
        darkValue: 'fade(#FFF, 4%)',
        cssvar: true,
      },
      {
        name: 'color-fill-2',
        desc: '常规/白底悬浮',
        descEN: 'Normal/White Hover',
        value: 'var(--color-neutral-2)',
        darkValue: 'fade(#FFF, 8%)',
        cssvar: true,
      },
      {
        name: 'color-fill-3',
        desc: '深/灰底悬浮',
        descEN: 'Deep/Gray Hover',
        value: 'var(--color-neutral-3)',
        darkValue: 'fade(#FFF, 12%)',
        cssvar: true,
      },
      {
        name: 'color-fill-4',
        desc: '重/特殊场景',
        descEN: 'Heavy/Special scene',
        value: 'var(--color-neutral-4)',
        darkValue: 'fade(#FFF, 16%)',
        cssvar: true,
      },
    ],
  },
  {
    name: '文字颜色',
    type: 'color',
    tokens: [
      {
        name: 'color-text-1',
        desc: '强调/正文标题',
        descEN: 'Emphasis/Body Title',
        value: 'var(--color-neutral-10)',
        darkValue: 'fade(#FFF, 90%)',
        cssvar: true,
      },
      {
        name: 'color-text-2',
        desc: '次强调/正文标题',
        descEN: 'Sub-Emphasis/Body Title',
        value: 'var(--color-neutral-8)',
        darkValue: 'fade(#FFF, 70%)',
        cssvar: true,
      },
      {
        name: 'color-text-3',
        desc: '次要信息',
        descEN: 'Secondary information',
        value: 'var(--color-neutral-6)',
        darkValue: 'fade(#FFF, 50%)',
        cssvar: true,
      },
      {
        name: 'color-text-4',
        desc: '置灰信息',
        descEN: 'Grayed out information',
        value: 'var(--color-neutral-4)',
        darkValue: 'fade(#FFF, 30%)',
        cssvar: true,
      },
    ],
  },
  {
    name: '背景颜色',
    nameEN: 'Background Color',
    type: 'color',
    tokens: [
      {
        name: 'color-bg-1',
        desc: '整体背景色',
        descEN: 'Overall background color',
        value: '#FFF',
        darkValue: '#17171A',
        cssvar: true,
      },
      {
        name: 'color-bg-2',
        desc: '一级容器背景',
        descEN: 'Primary container background',
        value: '#FFF',
        darkValue: '#232324',
        cssvar: true,
      },
      {
        name: 'color-bg-3',
        desc: '二级容器背景',
        descEN: 'Secondary container background',
        value: '#FFF',
        darkValue: '#2A2A2B',
        cssvar: true,
      },
      {
        name: 'color-bg-4',
        desc: '三级容器背景',
        descEN: 'Tertiary container background',
        value: '#FFF',
        darkValue: '#313132',
        cssvar: true,
      },
      {
        name: 'color-bg-5',
        desc: '下拉弹出框、Tooltip 背景颜色',
        descEN: 'Popup, Tooltip background color',
        value: '#FFF',
        darkValue: '#373739',
        cssvar: true,
      },
      {
        name: 'color-bg-white',
        desc: '白色背景',
        descEN: 'White background',
        value: '#FFF',
        darkValue: '#F6F6F6',
        cssvar: true,
      },
    ],
  },
  // size
  {
    name: '字体大小',
    nameEN: 'Font Size',
    type: 'size',
    tokens: [
      {
        name: 'font-size-body-3',
        desc: '正文-常规',
        descEN: 'Body - General',
        value: '14px',
      },
      {
        name: 'font-size-body-2',
        desc: '正文-常规-小',
        descEN: 'Body - Regular - Small',
        value: '13px',
      },
      {
        name: 'font-size-body-1',
        desc: '辅助文案/次要文案',
        descEN: 'Auxiliary Copywriting/Secondary Copywriting',
        value: '12px',
      },
      {
        name: 'font-size-caption',
        desc: '水印文本',
        descEN: 'Watermark text',
        value: '12px',
      },
      {
        name: 'font-size-title-1',
        desc: '标题-小',
        descEN: 'Title - Small',
        value: '16px',
      },
      {
        name: 'font-size-title-2',
        desc: '标题-中',
        descEN: 'Title - Medium',
        value: '20px',
      },
      {
        name: 'font-size-title-3',
        desc: '标题-大',
        descEN: 'Title - Large',
        value: '24px',
      },
      {
        name: 'font-size-display-1',
        desc: '运营标题-小',
        descEN: 'Operational Title - Small',
        value: '36px',
      },
      {
        name: 'font-size-display-2',
        desc: '运营标题-中',
        descEN: 'Operational Title - Medium',
        value: '48px',
      },
      {
        name: 'font-size-display-3',
        desc: '运营标题-大',
        descEN: 'Operational Title - Large',
        value: '56px',
      },
    ],
  },
  {
    name: '字重',
    nameEN: 'Font Wight',
    type: 'size',
    tokens: [
      {
        name: 'font-weight-400',
        desc: '常规',
        descEN: 'Normal',
        value: '400',
      },
      {
        name: 'font-weight-500',
        desc: '中等（加粗）',
        descEN: 'Medium (bold)',
        value: '500',
      },
      {
        name: 'font-weight-600',
        desc: '半粗',
        descEN: 'Half thick',
        value: '600',
      },
      {
        name: 'font-weight-700',
        desc: '粗体',
        descEN: 'Bold',
        value: '700',
      },
      {
        name: 'font-weight-800',
        desc: '中黑',
        descEN: 'Medium black',
        value: '800',
      },
      {
        name: 'font-weight-900',
        desc: '黑体',
        descEN: 'Black body',
        value: '900',
      },
      {
        name: 'font-weight-300',
        desc: '细体',
        descEN: 'Fine body',
        value: '300',
      },
      {
        name: 'font-weight-200',
        desc: '纤细',
        descEN: 'Slim',
        value: '200',
      },
      {
        name: 'font-weight-100',
        desc: '极细',
        descEN: 'Very fine',
        value: '100',
      },
    ],
  },
  {
    name: '尺寸',
    nameEN: 'Size',
    type: 'size',
    tokens: [
      {
        name: 'size-none',
        desc: '0',
        value: '0',
      },
      {
        name: 'size-1',
        desc: '4px',
        value: '4px',
      },
      {
        name: 'size-2',
        desc: '8px',
        value: '8px',
      },
      {
        name: 'size-3',
        desc: '12px',
        value: '12px',
      },
      {
        name: 'size-4',
        desc: '迷你（24px）',
        value: '16px',
      },
      {
        name: 'size-5',
        desc: '20px',
        value: '20px',
      },
      {
        name: 'size-N',
        desc: '变量对应的值为 (4 * N)px 大小',
        descEN: 'The corresponding value of the variable is (4 * N)px size',
        value: '(4 * N)px',
      },
      {
        name: 'size-50',
        desc: '200px',
        value: '200px',
      },
    ],
  },
  {
    name: '组件尺寸',
    nameEN: 'Component Size',
    type: 'size',
    tokens: [
      {
        name: 'size-mini',
        desc: '迷你（24px）',
        descEN: 'mini(24px)',
        value: '@size-6',
      },
      {
        name: 'size-small',
        desc: '较小（28px）',
        descEN: 'small(28px)',
        value: '@size-7',
      },
      {
        name: 'size-default',
        desc: '中等（32px）',
        descEN: 'medium(32px)',
        value: '@size-8',
      },
      {
        name: 'size-large',
        desc: '较大（36px）',
        descEN: 'large(36px)',
        value: '@size-9',
      },
    ],
  },
  {
    name: '边框宽度',
    nameEN: 'Border Size',
    type: 'size',
    tokens: [
      {
        name: 'border-none',
        desc: '无边框',
        descEN: 'None Border',
        value: '0',
      },
      {
        name: 'border-1',
        desc: '常规',
        descEN: 'Normal',
        value: '1px',
      },
      {
        name: 'border-2',
        desc: '较粗',
        descEN: 'Thicker',
        value: '2px',
      },
      {
        name: 'border-3',
        desc: '粗',
        descEN: 'Bold',
        value: '3px',
      },
    ],
  },
  {
    name: '边框圆角',
    nameEN: 'Border Radius',
    type: 'size',
    tokens: [
      {
        name: 'border-radius-none',
        desc: '直角',
        descEN: 'Right angle',
        value: '0',
        cssvar: true,
      },
      {
        name: 'border-radius-small',
        desc: '圆角-常规',
        descEN: 'Rounded Corners - Regular',
        value: '2px',
        cssvar: true,
      },
      {
        name: 'border-radius-medium',
        desc: '圆角-中等',
        descEN: 'Rounded Corners - Medium',
        value: '4px',
        cssvar: true,
      },
      {
        name: 'border-radius-large',
        desc: '圆角-较大',
        descEN: 'Rounded Corners - Larger',
        value: '8px',
        cssvar: true,
      },
      {
        name: 'border-radius-circle',
        desc: '圆角-全圆角',
        descEN: 'Rounded Corners - Full Rounded Corners',
        value: '50%',
        cssvar: true,
      },
    ],
  },
  {
    name: '阴影',
    nameEN: 'Shadow',
    type: 'size',
    tokens: [
      {
        name: 'shadow-special',
        desc: '特殊阴影',
        descEN: 'Special shade',
        value: '0 0 1px rgba(0, 0, 0, 0.3)',
      },
      {
        name: 'shadow1-center',
        desc: '阴影样式1',
        descEN: 'shadow style 1',
        value: '0 -2px 5px rgba(0, 0, 0, 0.1)',
      },
      {
        name: 'shadow2-center',
        desc: '阴影样式2',
        descEN: 'shadow style 2',
        value: '0 0 10px rgba(0, 0, 0, 0.1)',
      },
      {
        name: 'shadow3-center',
        desc: '阴影样式3',
        descEN: 'shadow style 3',
        value: '0 0 20px rgba(0, 0, 0, 0.1)',
      },
    ],
  },
];
