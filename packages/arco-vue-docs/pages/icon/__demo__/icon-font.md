```yaml
title:
  zh-CN: 使用 iconfont.cn 项目
  en-US: Using the iconfont.cn project
```

## zh-CN

可以把 iconfont.cn 中的项目添加到 `Icon` 组件的 `type` 中。
调用组件库暴露的工具方法 `Icon.addFromIconfontCn`，把 iconfont.cn 中导出 symbol 的代码传入 src 属性中加载项目图标库。
原理是创建了一个使用 `<use>` 标签来渲染图标的组件。
**暂不支持按需加载**

``` vue
<template>
  <icon-font type="icon-person" :size="32"/>
</template>

<script>
import { Icon } from '@arco-design/web-vue';

const IconFont = Icon.addFromIconFontCn({ src: 'https://at.alicdn.com/t/font_180975_ue66sq60vyd.js' });

export default {
  components:{
    IconFont
  }
}
</script>
```

### IconFontOptions

|参数名|描述|类型|默认值|
|---|---|---|:---:|
|src|iconfont.cn 项目生成的在线 js 文件地址|`string`|`-`|
|extraProps|传递给内部 Icon 组件的额外属性|`object`|`-`|

在 `src` 都设置有效的情况下，组件在渲染前会自动引入 [iconfont.cn](http://iconfont.cn/ "_blank") 项目中的图标符号集，无需手动引入。
见 [iconfont.cn](https://www.iconfont.cn/help/detail?spm=a313x.7781069.1998910419.15&helptype=code "_blank") 使用帮助: 查看如何生成 js 地址。

### `<icon>` Props

|Attribute|Description|Type|Default|
|---|---|---|:---:|
|type|图标类型|`string`|`-`|
|size|大小|`number` \| `string`|`-`|
|rotate|旋转角度|`number`|`-`|
|spin|是否旋转|`boolean`|`false`|

---

## en-US

Items from iconfont.cn can be added to the `type` of the `Icon` component.
Call the tool method `Icon.addFromIconfontCn` exposed by the component library, and pass the code that exports the symbol in [iconfont.cn](http://iconfont.cn/ "_blank") into the src attribute to load the project icon library.
The idea is to create a component that uses the `<use>` tag to render the icon.
** Currently does not support on-demand loading **

### IconFontOptions

|Name|Description|Type|Default|
|---|---|---|:---:|
|src|The online js file address generated by the iconfont.cn project|`string`|`-`|
|extraProps|Extra properties passed to the inner Icon component|`object`|`-`|

When both `src` settings are valid, the component automatically introduces the icon symbol set in the iconfont.cn project before rendering, eliminating the need for manual introduction.
See [iconfont.cn](https://www.iconfont.cn/help/detail?spm=a313x.7781069.1998910419.15&helptype=code "_blank") for help on how to generate js addresses.

### `<icon>` Props

|Attribute|Description|Type|Default|
|---|---|---|:---:|
|type|Icon type|`string`|`-`|
|size|Size|`number` \| `string`|`-`|
|rotate|Rotate angle|`number`|`-`|
|spin|Whether to spin|`boolean`|`false`|

---

```vue
<template>
  <a-space size="large">
    <icon-font type="icon-person" :size="32"/>
    <icon-font type="icon-earth" :size="32"/>
    <icon-font type="icon-flag" :size="32"/>
  </a-space>
</template>

<script>
import { Icon } from '@arco-design/web-vue';

const IconFont = Icon.addFromIconFontCn({ src: 'https://at.alicdn.com/t/font_180975_ue66sq60vyd.js' });

export default {
  components:{
    IconFont
  }
}
</script>
```
