```yaml
meta:
  type: Arco Pro
title: Internationalization
description: Multi-language implementation
```

*Auto translate by google.*

## Language Pack

Internationalization is firstly the provision of language packs. In Pro, the language packs are defined in `src/locale`, and then imported into `main` to take effect.

```
├── locale
│ ├── en-US.ts
│ └── zh-CN.ts
├── hooks
│ ├── locale.ts
└── main.ts
```

At the same time, hooks for obtaining the current language and switching the current language are provided in the hooks directory.

 ```ts
import {computed} from'vue';
import {useI18n} from'vue-i18n';

export default function useLocale() {
     const i18 = useI18n();
     const currentLocale = computed(() => {
         return i18.locale.value;
     });
     const changeLocale = (value: string) => {
         i18.locale.value = value;
         localStorage.setItem('arco-locale', value);
     };
     return {
         currentLocale,
         changeLocale,
     };
}
```
