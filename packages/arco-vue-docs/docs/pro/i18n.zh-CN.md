```yaml
meta:
  type: Arco Pro 最佳实践
title: 国际化
description: 多语言实现
```

## 语言包

国际化首先是语言包的提供，Pro 里面将语言包定义在 `src/locale` 中，然后导入 `main` 中生效。

```
├── locale
│ ├── en-US.ts
│ └── zh-CN.ts
├── hooks
│ ├── locale.ts
└── main.ts
```

同时在hooks目录中提供获取当前语言以及切换当前语言的hook。

 ```ts
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

export default function useLocale() {
    const i18 = useI18n();
    const currentLocale = computed(() => {
        return i18.locale.value;
    });
    const changeLocale = (value: string) => {
        i18.locale.value = value;
        localStorage.setItem('arco-locale', value);
    };
    return {
        currentLocale,
        changeLocale,
    };
}
```
