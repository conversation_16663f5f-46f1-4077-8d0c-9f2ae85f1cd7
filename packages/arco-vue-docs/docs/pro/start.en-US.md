```yaml
meta:
  type: Arco Pro
title: Quick start
description: Please follow the steps below to create Arco Design Pro
```

*Auto translate by google.*

## Environment

Before starting development, please make sure that `node`, `git` and `arco cli` are installed in the local environment.

Among them, `arco cli` is a tool for installing project templates, please run the following command to install:

```bash
npm i -g arco-cli
```

## Technology Stack

The technology stack of this project is `vue` + `ES2015+` + `TypeScript` + `Arco Design` and `echarts`, etc. Learning and understanding this knowledge in advance will help you get started with our project better .

## Install

This step uses Arco Design Pro as a template to create a new project, please follow the steps below:

- Go to a folder and create a new project

```bash
cd someDir
arco init hello-arco-pro
```

- Choose a technology stack

```bash
 ? Please select the technology stack you wish to use
   React
 ❯ Vue
```

- Choose `arco-design-pro` category

```bash
 ? please choose a category
   Business component
   Component library
   Lerna Menorepo project
 ❯ Arco Pro project
```

Wait for the dependencies to be installed. . .

When you see the following output, the creation is successful

![](https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/8b78dd4bbdba4bf7939bd0a131357b31~tplv-uwbnlip3yd-image.image)

## Development

Enter the project, run the code

```bash
cd hello-arco-pro

npm run dev
```

Open [localhost:3000](http://localhost:3000) and you will see the following page

![](https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/1e331a3b8e2446e2be6c78b1c86e5e50~tplv-uwbnlip3yd-image.image)
