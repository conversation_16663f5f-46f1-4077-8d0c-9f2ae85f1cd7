```yaml
meta:
  type: 开发指南
title: 国际化
description: 所有组件文案默认使用的是中文，通过设置可以使用其它语言。
```

通过 [ConfigProvider](/vue/component/config-provider) 组件实现国际化。

## 基本用法

```vue
<template>
  <a-config-provider :locale="enUS">
    <a-pagination :total="50" show-total show-jumper show-page-size />
  </a-config-provider>
</template>

<script>
import enUS from '@arco-design/web-vue/es/locale/lang/en-us';

export default {
  data() {
    return {
      enUS,
    };
  },
};
</script>
```

## 支持的地区语言

| 语言                 | 地区编码 |
| -------------------- | -------- |
| 简体中文             | zh-CN    |
| 英文                 | en-US    |
| 日文                 | ja-JP    |
| 繁体中文（中国台湾） | zh-TW    |
| 葡萄牙语             | pt-PT    |
| 西班牙语             | es-ES    |
| 印度尼西亚语         | id-ID    |
| 法语（法国）         | fr-FR    |
| 德语（德国）         | de-DE    |
| 韩语                 | ko-KR    |
| 意大利语(意大利)     | it-IT    |
| 马来语(马来西亚)     | ms-MY    |
| 泰语                 | th-TH    |
| 越南语               | vi-VN    |
| 高棉语（柬埔寨）       | km-KH    |
| 阿拉伯语（埃及）       | ar-EG    |
| 俄语（俄罗斯）       | ru-RU    |
| 荷兰语（荷兰）       | nl-NL    |
