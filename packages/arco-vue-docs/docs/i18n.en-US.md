```yaml
meta:
  type: Developer Guide
title: Globalization
description: All component texts use Chinese by default, and other languages can be used by setting.
```

_Auto translate by google._

Internationalization is achieved through the [ConfigProvider](/vue/component/config-provider) component.

## Basic usage

```vue
<template>
  <a-config-provider :locale="enUS">
    <a-pagination :total="50" show-total show-jumper show-page-size />
  </a-config-provider>
</template>

<script>
import enUS from '@arco-design/web-vue/es/locale/lang/en-us';

export default {
  data() {
    return {
      enUS,
    };
  },
};
</script>
```

## Supported regional languages

| Language            | Area code |
| ------------------- | --------- |
| Simple Chinese      | zh-CN     |
| English (US)        | en-US     |
| Japanese            | ja-JP     |
| Traditional Chinese | zh-TW     |
| Portuguese          | pt-PT     |
| Spanish             | es-ES     |
| Indonesian          | id-ID     |
| French, France      | fr-FR     |
| German, Germany     | de-DE     |
| Korean              | ko-KR     |
| Italian, Italy      | it-IT     |
| Thai                | th-TH     |
| Melayu (Malaysia)   | ms-MY     |
| Vietnamese          | vi-VN     |
| Khmer (Cambodia)    | km-KH     |
| Arabic (Egypt)      | ar-EG     |
| Russian (Russia)    | ru-RU     |
| Dutch (Netherlands) | nl-NL     |
