export default {
  docs: {
    name: '开发指南',
    start: '快速上手',
    dark: '暗黑模式',
    theme: '定制主题',
    token: '设计变量',
    i18n: '国际化',
    faq: '常见问题',
    changelog: '更新日志',
  },
  group: {
    common: '通用',
    layout: '布局',
    dataDisplay: '数据展示',
    dataEntry: '数据输入',
    feedback: '反馈',
    navigation: '导航',
    other: '其他',
  },
  component: {
    name: '组件',
    // 组件名称
    calendar: '日历 Calendar',
    icon: '图标 Icon',
    button: '按钮 Button',
    link: '链接 Link',
    typography: '排版 Typography',
    empty: '空状态 Empty',
    badge: '徽标 Badge',
    table: '表格 Table',
    tooltip: '文字气泡 Tooltip',
    popover: '气泡卡片 Popover',
    input: '输入框 Input',
    inputNumber: '数字输入框 InputNumber',
    radio: '单选框 Radio',
    checkbox: '复选框 Checkbox',
    switch: '开关 Switch',
    alert: '警告提示 Alert',
    result: '结果页 Result',
    spin: '加载中 Spin',
    message: '全局提示 Message',
    drawer: '抽屉 Drawer',
    breadcrumb: '面包屑 Breadcrumb',
    pageHeader: '页头 PageHeader',
    notification: '通知提醒框 Notification',
    popconfirm: '气泡确认框 Popconfirm',
    modal: '对话框 Modal',
    dropdown: '下拉菜单 Dropdown',
    backTop: '返回顶部 BackTop',
    trigger: '触发器 Trigger',
    anchor: '锚点 Anchor',
    affix: '固钉 Affix',
    grid: '栅格 Grid',
    space: '间距 Space',
    divider: '分割线 Divider',
    progress: '进度条 Progress',
    steps: '步骤条 Steps',
    skeleton: '骨架屏 Skeleton',
    slider: '滑动输入条 Slider',
    rate: '评分 Rate',
    tag: '标签 Tag',
    select: '选择器 Select',
    inputTag: '标签输入框 InputTag',
    autoComplete: '自动补全 AutoComplete',
    avatar: '头像 Avatar',
    card: '卡片 Card',
    carousel: '图片轮播 Carousel',
    timeline: '时间轴 Timeline',
    comment: '评论 Comment',
    layout: '布局 Layout',
    menu: '菜单 Menu',
    resizeBox: '伸缩框 ResizeBox',
    split: '面板分割 Split',
    statistic: '数值显示 Statistic',
    form: '表单 Form',
    collapse: '折叠面板 Collapse',
    descriptions: '描述列表 Descriptions',
    pagination: '分页 Pagination',
    datePicker: '日期选择器 DatePicker',
    timePicker: '时间选择器 TimePicker',
    image: '图片 Image',
    configProvider: '全局配置 ConfigProvider',
    upload: '上传 Upload',
    cascader: '级联选择 Cascader',
    tabs: '标签页 Tabs',
    list: '列表 List',
    mention: '提及 Mention',
    textarea: '文本域 Textarea',
    transfer: '数据穿梭框 Transfer',
    tree: '树 Tree',
    treeSelect: '树选择 TreeSelect',
    overflow: '折叠列表 OverflowList',
    scrollbar: '滚动条 Scrollbar',
    watermark: '水印 Watermark',
    verificationCode: '验证码输入框 VerificationCode',
    colorPicker: '颜色选择器 ColorPicker',
  },
  footer: {
    design: '设计',
    component: '组件',
    ecosystem: '生态产品',
    resource: '资源',
    feedback: '反馈',
    about: '关于 Arco',
    overview: '组件索引',
    spec: '设计规范',
    principle: '设计原则',
    start: '快速开始',
    question: '常见问题',
    changelog: '更新日志',
    pro: 'ArcoPro 中后台最佳实践',
    componentFigma: '组件库 Figma 资源',
    pluginSparrow: 'Sparrow Figma 插件',
    iconFigma: '图标 Figma 资源',
    toolMigrate: '迁移工具',
    toolArcoCli: 'Arco CLI',
    toolWebpack: 'Webpack Plugin',
    designLab: 'DesignLab 设计系统配置平台',
    material: 'ArcoMaterial 物料平台',
    chartSpace: 'ChartSpace 图表库',
    fontMall: 'FontMall 字体库',
    brand: 'BrandStore 品牌资源',
    mobile: 'ArcoMobile 移动端组件库',
    team: '团队介绍',
    arcoNews: '双周小黑板',
    cli: 'Arco CLI',
    dashboard: 'Dashboard',
  },
  tooltip: {
    expand: '展开代码',
    collapse: '收起代码',
    copy: '复制代码',
    stackblitz: '在 Stackblitz 打开',
    codeSandbox: '在 CodeSandbox 打开',
  },
  icon: {
    styleAll: '全部',
    styleOutline: '线性图标',
    styleFill: '面性图标',
    styleColor: '多色图标',
    searchPlaceholder: '搜索图标，点击可复制图标用法',
    iconClassifyModalTitle: '上传图片搜索图标',
    showConfig: '显示配置',
    configTitle: '图标配置',
    configDesc1: '全局配置（将以下的类添加到 css 中）:',
    configDesc2: `单个组件的话可以直接将以上样式写到 <code>IconXXX</code> 的 <code>style</code> 中`,
    strokeWidth: '线宽',
    fontSize: '图标大小',
    strokeLinejoin: '拐角',
    strokeLinecap: '端点',
    direction: '方向指示类图标',
    tips: '提示建议类图标',
    interactiveButton: '交互按钮类图标',
    edit: '编辑类图标',
    media: '影音类图标',
    logo: '商标类图标',
    general: '通用类图标',
  },
  proDocs: {
    name: 'Arco Pro 最佳实践',
    start: '快速上手',
    npmScripts: 'Npm 命令',
    directory: '目录结构',
    layout: '布局',
    routesAndMenu: '路由和菜单',
    stateManagement: '状态管理-Vuex（已迁移）',
    stateManagementPinia: '状态管理-Pinia',
    i18n: '国际化',
    mock: '接口和数据模拟',
    build: '打包构建',
    permission: '权限控制',
    faq: '常见问题',
  },
  themeBox: {
    currentTheme: '当前主题',
    autoUseTheme: '自动应用主题',
    install: '安装',
    installTheme: '安装主题',
    search: '搜索',
    installingTheme: '正在安装主题...',
    installThemeSuccess: '主题安装成功！',
    installThemeError: '主题安装失败，请重试！',
    resetTheme: '重置主题',
    resetThemeSuccess: '重置主题成功！',
    openInDesignLab: '在主题商店打开',
    noResult: '没有相关主题',
    createTheme: '前往主题商店创建',
  },
  changelogBox: {
    changelog: '更新记录',
    version: '按版本',
    date: '按日期',
    attention: '重点注意',
    enhancement: '功能优化',
    feature: '功能升级',
    bugfix: '问题修复',
    style: '样式更新',
    typescript: '类型修正',
    chore: '架构改动',
    to: '到',
    filter: '筛选选项',
  },
};
