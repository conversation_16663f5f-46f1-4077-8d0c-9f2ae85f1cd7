export default {
  docs: {
    name: 'Developer Guide',
    start: 'Quick Start',
    dark: 'Dark Mode',
    theme: 'Custom Theme',
    token: 'Design Token',
    i18n: 'Internationalization',
    faq: 'FAQ',
    changelog: 'Changelog',
  },
  group: {
    common: 'Common',
    layout: 'Layout',
    dataDisplay: 'Data Display',
    dataEntry: 'Data Entry',
    feedback: 'Feedback',
    navigation: 'Navigation',
    other: 'Other',
  },
  component: {
    name: 'Component',
    // 组件名称
    icon: 'Icon',
    button: 'Button',
    link: 'Link',
    typography: 'Typography',
    radio: 'Radio',
    checkbox: 'Checkbox',
    switch: 'Switch',
    anchor: 'Anchor',
    empty: 'Empty',
    badge: 'Badge',
    table: 'Table',
    tooltip: 'Tooltip',
    popover: 'Popover',
    input: 'Input',
    calendar: 'Calendar',
    inputNumber: 'InputNumber',
    alert: 'Alert',
    result: 'Result',
    spin: 'Spin',
    message: 'Message',
    notification: 'Notification',
    popconfirm: 'Popconfirm',
    modal: 'Modal',
    dropdown: 'Dropdown',
    backTop: 'BackTop',
    trigger: 'Trigger',
    affix: 'Affix',
    grid: 'Grid',
    space: 'Space',
    divider: 'Divider',
    drawer: 'Drawer',
    breadcrumb: 'Breadcrumb',
    pageHeader: 'PageHeader',
    progress: 'Progress',
    steps: 'Steps',
    skeleton: 'Skeleton',
    slider: 'Slider',
    rate: 'Rate',
    avatar: 'Avatar',
    card: 'Card',
    carousel: 'Carousel',
    timeline: 'Timeline',
    comment: 'Comment',
    tag: 'Tag',
    select: 'Select',
    inputTag: 'InputTag',
    autoComplete: 'AutoComplete',
    layout: ' Layout',
    menu: 'Menu',
    resizeBox: 'ResizeBox',
    split: 'Split',
    statistic: 'Statistic',
    form: 'Form',
    collapse: 'Collapse',
    descriptions: 'Descriptions',
    pagination: 'Pagination',
    datePicker: 'DatePicker',
    timePicker: 'TimePicker',
    configProvider: 'ConfigProvider',
    image: 'Image',
    upload: 'Upload',
    mention: 'Mention',
    textarea: 'Textarea',
    cascader: 'Cascader',
    tabs: 'Tabs',
    list: 'List',
    transfer: 'Transfer',
    tree: 'Tree',
    treeSelect: 'TreeSelect',
    overflow: 'OverflowList',
    scrollbar: 'Scrollbar',
    watermark: 'Watermark',
    verificationCode: 'VerificationCode',
    colorPicker: 'ColorPicker',
  },
  footer: {
    design: 'Design',
    component: 'Component',
    ecosystem: 'Ecosystem',
    resource: 'Resource',
    feedback: 'Feedback',
    about: 'About Arco',
    overview: 'Overview',
    spec: 'Specification',
    principle: 'Principles',
    start: 'Quick Start',
    question: 'Question',
    changelog: 'Changelog',
    pro: 'ArcoPro Best Practices',
    componentFigma: 'Figma Of Component',
    pluginSparrow: 'Figma Plugin Of Sparrow',
    iconFigma: 'Figma Of Icon',
    toolMigrate: 'Code migration',
    toolArcoCli: 'Arco CLI',
    toolWebpack: 'Webpack Plugin',
    designLab: 'DesignLab Design System Lab',
    material: 'ArcoMaterial Component Market',
    chartSpace: 'ChartSpace Arco Chart Space',
    fontMall: 'FontMall FontMall',
    brand: 'BrandStore BrandStore',
    mobile: 'ArcoMobile React Mobile',
    team: 'Arco Team',
    arcoNews: 'Arco News',
    cli: 'Arco CLI',
    dashboard: 'Dashboard',
  },
  tooltip: {
    expand: 'Expand code',
    collapse: 'Collapse code',
    copy: 'Copy code',
    stackblitz: 'Open in Stackblitz',
    codeSandbox: 'Open in CodeSandbox',
  },
  icon: {
    styleAll: 'All',
    styleOutline: 'Stroke',
    styleFill: 'Fill',
    styleColor: 'Color',
    searchPlaceholder: 'Search icon, click to copy usage',
    iconClassifyModalTitle: 'Upload an image to search for icons',
    showConfig: 'Show Config',
    configTitle: 'Icon Config',
    configDesc1: 'Global configuration (add the following class to css):',
    configDesc2: `For a single component, you can directly write the above style to the <code>style</code>
    of <code>IconXXX</code>`,
    strokeWidth: 'Stroke width',
    fontSize: 'Size',
    strokeLinejoin: 'Line Join',
    strokeLinecap: 'Line Cap',
    direction: 'Direction indicator icon',
    tips: 'Prompt suggestion icon',
    interactiveButton: 'Interactive button icon',
    edit: 'Edit icon',
    media: 'Multimedia icon',
    logo: 'Trademark icon',
    general: 'Universal icon',
  },
  proDocs: {
    name: 'Arco Pro',
    start: 'Quick start',
    npmScripts: 'Npm Scripts',
    directory: 'Directory',
    layout: 'Layout',
    routesAndMenu: 'Routes and menu',
    stateManagement: 'State management - Vuex',
    stateManagementPinia: 'State management - Pinia',
    i18n: 'Internationalization',
    mock: 'Interface and Mock',
    build: 'Build',
    permission: 'Permission Control',
    faq: 'FAQ',
  },
  themeBox: {
    currentTheme: 'Current theme',
    autoUseTheme: 'Automatically use theme',
    install: 'Install',
    installTheme: 'Install theme',
    search: 'Search',
    installingTheme: 'Installing theme...',
    installThemeSuccess: 'Install theme successfully! ',
    installThemeError: 'Install theme failed, please try again! ',
    resetTheme: 'Reset theme',
    resetThemeSuccess: 'Reset theme successfully! ',
    openInDesignLab: 'Open in the Design Lab',
    noResult: 'No related themes',
    createTheme: 'Go to the Design Lab to create',
  },
  changelogBox: {
    changelog: 'Changelog',
    version: 'By version',
    date: 'By Date',
    attention: 'Attention',
    enhancement: 'Enhancement',
    feature: 'Feature',
    bugfix: 'Bugfix',
    style: 'Style',
    typescript: 'Typescript',
    chore: 'Chore',
    to: 'To',
    filter: 'Filter',
  },
};
