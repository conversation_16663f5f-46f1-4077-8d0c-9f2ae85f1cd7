{"name": "@arco-design/arco-changelog", "version": "0.3.2", "description": "Arco Changelog", "author": "ArcoDesign Team", "license": "MIT", "main": "dist/index.js", "bin": "dist/cmd.js", "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/arco-design/arco-design-vue.git"}, "scripts": {"dev": "tsc --watch", "build": "tsc && node copy-template.js", "lint-staged": "npx lint-staged"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --fix", "prettier --write"]}, "devDependencies": {"@types/fs-extra": "^9.0.13", "@types/inquirer": "^7.3.3", "@types/nunjucks": "^3.2.6", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "eslint": "^7.32.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-config-prettier": "^8.10.0", "eslint-import-resolver-typescript": "^2.7.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^3.4.1", "prettier": "^2.8.8", "ts-node": "^10.9.2"}, "dependencies": {"axios": "^0.21.4", "chalk": "^4.1.2", "commander": "^7.2.0", "comment-parser": "^1.4.1", "fast-glob": "^3.3.3", "fs-extra": "^9.1.0", "glob": "^7.2.3", "inquirer": "^8.2.6", "moment": "^2.30.1", "nunjucks": "^3.2.4", "ora": "^5.4.1", "typescript": "^4.9.5"}}