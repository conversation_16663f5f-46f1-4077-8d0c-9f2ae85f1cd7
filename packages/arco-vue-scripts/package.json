{"name": "@arco-design/arco-vue-scripts", "version": "0.2.10", "description": "<PERSON><PERSON>", "author": "ArcoDesign Team", "license": "MIT", "main": "dist/index.js", "bin": "dist/index.js", "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/arco-design/arco-design-vue.git"}, "scripts": {"dev": "tsc --watch", "build": "tsc && node copy-template.js", "lint-staged": "npx lint-staged"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --fix", "prettier --write"]}, "devDependencies": {"@babel/types": "^7.26.9", "@types/clean-css": "^4.2.11", "@types/fs-extra": "^9.0.13", "@types/inquirer": "^7.3.3", "@types/jest": "^26.0.24", "@types/jsdom": "^16.2.15", "@types/less": "^3.0.8", "@types/nunjucks": "^3.2.6", "@types/svgo": "^2.6.4", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "eslint": "^7.32.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-config-prettier": "^8.10.0", "eslint-import-resolver-typescript": "^2.7.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^3.4.1", "prettier": "^2.8.8", "ts-node": "^10.9.2"}, "dependencies": {"@arco-design/vite-plugin-arco-vue-docs": "workspace:*", "@babel/core": "^7.26.9", "@babel/parser": "^7.26.9", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/preset-env": "^7.26.9", "@babel/preset-typescript": "^7.26.0", "@vitejs/plugin-vue": "^1.10.2", "@vitejs/plugin-vue-jsx": "^1.3.10", "@vue/babel-plugin-jsx": "^1.4.0", "axios": "^0.21.4", "babel-jest": "^26.6.3", "chalk": "^4.1.2", "clean-css": "^5.3.3", "commander": "^7.2.0", "comment-parser": "^1.4.1", "fast-glob": "^3.3.3", "fs-extra": "^9.1.0", "glob": "^7.2.3", "inquirer": "^8.2.6", "jest": "^26.6.3", "jsdom": "^16.7.0", "less": "^4.2.2", "moment": "^2.30.1", "nunjucks": "^3.2.4", "ora": "^5.4.1", "rollup-plugin-terser": "^7.0.2", "svgo": "^2.8.0", "ts-jest": "^26.5.6", "ts-morph": "^11.0.3", "typescript": "^4.9.5", "vite": "^2.9.18", "vite-plugin-eslint": "^1.8.1", "vite-svg-loader": "^2.2.0", "vue-docgen-api": "4.40.0", "vue-jest": "5.0.0-alpha.10"}}