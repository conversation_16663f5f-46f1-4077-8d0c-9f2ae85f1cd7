# Arco Vue Scripts

A collection of commands used in the Arco Design Vue component library

## Installation

```
npm install @arco-design/arco-vue-scripts
```

## Commands

Run `arco-vue-scripts --help` to see all available commands and options.

+ `arco-vue-scripts docgen`: generate document of component.
+ `arco-vue-scripts icongen`: generate icon components.
+ `arco-vue-scripts lessgen`: generate index less file.
+ `arco-vue-scripts dtsgen`: emit .d.ts files for vue files.
+ `arco-vue-scripts dev:component`: build components with watch mode.
+ `arco-vue-scripts dev:site`: start vite server for development.
+ `arco-vue-scripts build:component`: build production files.
+ `arco-vue-scripts build:style`: build style related files.
+ `arco-vue-scripts build:site`: build document site.
+ `arco-vue-scripts build:material`: build vue material.
+ `arco-vue-scripts test`: run test for component or material.
+ `arco-vue-scripts changelog`: Obtain and organize changelog information through the git repository.
