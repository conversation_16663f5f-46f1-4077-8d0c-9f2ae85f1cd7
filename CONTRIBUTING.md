
> English | [简体中文](./CONTRIBUTING.zh-CN.md)
# Contributing

Thank you for taking your time to contribute and make this project better! Here are some guidelines to help you get started. Please make sure to take a moment and read through them before submitting your contributions.

## Code of Conduct

This project is governed by the [Contributor Covenant Code of Conduct](./CODE_OF_CONDUCT.md). By participating, you are expected to adhere to it.

## Open Development

All work happens directly on GitHub. Both core team members and external contributors send pull requests which go through the same review process.

## Semantic Versioning

This project follows semantic versioning. We release patch versions for bug fixes or other changes that do not change the behavior of the API, minor versions for new features that are backward-compatible, and major versions for any breaking changes.

Every significant change is documented in the changelog file.

## Reporting Issues

We use [Github issues](https://github.com/arco-design/arco-design-vue/issues) for bug reports and feature requests. Before reporting an issue, please make sure you have searched for similar [issues](https://github.com/arco-design/arco-design-vue/issues) as they may have been already answered or being fixed. A new issue should be submitted via [issue helper](https://arco.design/issue-helper?repo=arco-design-vue). For bug reporting, please include the minimum code that can be used to reproduce the problem. For feature request, please specify what changes you want and what behavior you expect.

## Sending a pull request
This project uses [pnpm](https://pnpm.io/) for package management. Please install Yarn before development.

### Contribution Process

- **Claim an Issue**: Create an issue on GitHub and claim it (or directly claim an existing issue) to let others know you are working on it, preventing duplicate efforts.
- **Development**: After preparing for development, proceed with bug fixes or feature implementation.
- **Add Unit Tests**: Write unit tests for your code changes and ensure all test cases pass, aiming for reasonable test coverage.
- **Update Snapshots**: If your changes affect the component’s DOM structure, class names, or add/remove demos, update the snapshots accordingly.
- **Generate Documentation**: If the component API is modified, run `pnpm run docgen` to regenerate the documentation.
- **Submit a Pull Request**

### Development

1. Fork [this repository](https://github.com/arco-design/arco-design-vue) and create a branch from `main`.
   - For new features, submit a pull request to the `feature` branch.
   - For other changes, submit to the `main` branch.

```bash
<NAME_EMAIL>:arco-design/arco-design-vue.git
```

2. Install the dependencies of each package in `workspaces`.

```bash
pnpm install
```

3. Initialize the project.

```bash
pnpm run init
```

4. Start and preview the site.

```bash
pnpm run start
```

```bash
# Start Storybook (optional, as debugging can also be done on the official website)
pnpm run storybook
```

5. Make changes to the codebase. If applicable, ensure that you have written the corresponding tests.
6. Make sure all tests pass after running `pnpm run test`.
7. Commit your changes, adhering to the [Commit Guidelines](#commit-guidelines).
8. Open a new pull request, [referencing corresponding issues](https://docs.github.com/en/issues/tracking-your-work-with-issues/linking-a-pull-request-to-an-issue#linking-a-pull-request-to-an-issue-using-a-keyword) if available.


## Commit Guidelines

Commit messages are required to follow the [conventional-changelog standard](https://www.conventionalcommits.org/en/v1.0.0/):

```bash
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### Commit types

The following is a list of commit types:

- feat: A new feature or functionality
- fix: A bug fix
- docs: Documentation only changes
- style: Code formatting or component style changes
- refactor: Code changes that neither fixes a bug nor adds a feature.
- perf: Improve performance.
- test: Add missing or correct existing tests.
- chore: Other commits that don’t modify src or test files.


## Arco-Vue Repository Structure

This repository is managed by pnpm workspace and includes the following packages:

1. `web-vue`: UI component library
2. `vue-site`: Component documentation site
3. `arco-vue-scripts`: Component scripts
4. `arco-vue-md-loader`: Webpack loader for markdown parsing
5. `arco-vue-site-nav`: Navigation bar of documentation site (using React materials)

### Web-Vue Component Directory

> components/componentName

```
├── README.zh-CN.md (Note:Don't edit this file, it's generated by script)
├── README.en-US.md (Note:Don't edit this file, it's generated by script)
├── TEMPLATE.md (Template used to generate README file)
├── __test__
│   ├── __snapshots__
│   │   └── demo.test.js.snap (Snapshot)
│   ├── demo.test.ts (Snapshot test)
│   └── index.test.ts (Unit test)
├── __demo__ (Demos for each component)
│   ├── basic.md
│   └── advanced.md
├── index.tsx(Component export)
└── style
    └── index.less(Component style)
    └── index.ts (Component style entry)
```

Please note that if you make changes that will affect README (e.g. API changes), make sure to run `npm run docgen` to update README of the component.

For scripts related to the component library, please run under the `web-vue` directory.

## License

By contributing your code to the repository, you agree to license your contribution under the [MIT license](./LICENSE).
